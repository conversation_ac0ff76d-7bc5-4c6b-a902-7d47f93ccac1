﻿using Newtonsoft.Json;

namespace MicroSaasWebApi.Models.AlphaCCO
{
    /// <summary>
    /// Summary description for AlphaCCO
    /// </summary>
    [Serializable]
    public class AlphaCCOModel
    {
        public AlphaCCOModel()
        {
            //
            // TODO: Add constructor logic here
            //
        }
    }
    [Serializable]
    public class AlphaCCOLogin
    {
        public string? userName { get; set; }
        public string? password { get; set; }
    }

    public class AlphaCCOExternalParty
    {
        public string? email { get; set; }
        public string? firstName { get; set; }
        public string? lastName { get; set; }
        public string? externalSystemId { get; set; }
        public string? language { get; set; }
        public string? isactive { get; set; }

    }
    [Serializable]
    public class AlphaCCOSearch
    {
        public string? workflowkey { get; set; }
        public string? workflowInstancekey { get; set; }
        public string? externalPartyKey { get; set; }
        public string? userKey { get; set; }
        public string? reason { get; set; }
        public List<Override>? dataTagOverrides { get; set; }
    }
    [Serializable]
    public class AlphaCCoWorkflowStart
    {
        public string? workflowkey { get; set; }
        public string? name { get; set; }
        public string? externalPartyKey { get; set; }
        public List<Override>? dataTagOverrides { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string[]? linkedWorkflows { get; set; }  //comma seperataed workflow instance Ids,
    }

    [Serializable]
    public class Override
    {
        public string? code;
        public string? value;
    }
    [Serializable]
    public class AlphaCCOResponseBase
    {
        public string? StatusCode { get; set; }
        public string? StatusString { get; set; }
    }
    [Serializable]
    public class AlphaCCOAuthenticationResponse : AlphaCCOResponseBase
    {
        public string? firstName { get; set; }
        public string? lastName { get; set; }
        public string? token { get; set; }
    }


    [Serializable]
    public class AlphaCCOExternalPartyResponse : AlphaCCOResponseBase
    {
        public string? key { get; set; }
        public string? email { get; set; }
        public string? firstName { get; set; }
        public string? lastName { get; set; }
        public string? externalSystemId { get; set; }
        public string? language { get; set; }
        public string? isActive { get; set; }
    }
    [Serializable]
    public class AlphaCCOSearchResultResponse : AlphaCCOResponseBase
    {
        public int surveyId { get; set; }
    }

    [Serializable]
    public class AlphaCCoWorkflowStartResponse : AlphaCCOResponseBase
    {
        public string? workflowInstanceKey { get; set; }
    }
    [Serializable]
    public class AlphaCCOWorkflowResponse
    {
        public string? workflowInstanceKey { get; set; }
        public string? name { get; set; }
        public string? statusCode { get; set; }
        public string? statusDescription { get; set; }
        public DateTime startDate { get; set; }
        public object? endDate { get; set; }
        public object? currentTaskId { get; set; }
        public string? workflowKey { get; set; }
        public string? workflowName { get; set; }
        public int workflowVersion { get; set; }
        public object? startedByWorkflowInstanceKey { get; set; }
        public List<Step>? steps { get; set; }
    }
    [Serializable]
    public class WorflowTask
    {
        public int taskId { get; set; }
        public string? registerKey { get; set; }
        public string? registerName { get; set; }
        public string? status { get; set; }
        public string? assigedToUser { get; set; }
    }
    [Serializable]
    public class Step
    {
        public string? name { get; set; }
        public string? statusCode { get; set; }
        public string? statusDescription { get; set; }
        public DateTime startDate { get; set; }
        public object? endDate { get; set; }
        public int percentComplete { get; set; }
        public object? lastStatusChangeDate { get; set; }
        public List<WorflowTask>? tasks { get; set; }
    }

}