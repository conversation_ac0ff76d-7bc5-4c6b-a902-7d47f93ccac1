﻿using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Services.PXW.Interface;

namespace MicroSaasWebApi.Controllers.PXW
{
    [Route("api/[controller]")]
    [ApiController]
    public class BaseController : ControllerBase
    {
        protected readonly IBaseService _baseService;
        protected readonly ILoggerService _loggerService;
        public BaseController(IBaseService baseService, ILoggerService loggerService)
        {
            _baseService = baseService;
            _loggerService = loggerService;
        }
    }
}
