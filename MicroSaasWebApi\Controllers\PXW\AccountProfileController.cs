﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.AlphaCCO;
using MicroSaasWebApi.Models.Profile;
using MicroSaasWebApi.Models.PXW;
using MicroSaasWebApi.Properties.PXW;
using MicroSaasWebApi.Services.Profile.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using Swashbuckle.AspNetCore.Annotations;
using Swashbuckle.AspNetCore.Filters;

namespace MicroSaasWebApi.Controllers.PXW
{
    /// <summary>
    /// Account Profiles Endpoints.
    /// </summary>

    //[Authorize]
    [Route("api/PXW/[controller]")]
    [ApiController]
    [EnableCors("AllowAll")]
    public class AccountProfileController : BaseController
    {
        private readonly IApplicationSettingsService _applicationSettingsService;
        public AccountProfileController(IApplicationSettingsService applicationSettingsService,
            IBaseService baseService, ILoggerService loggerService) : base(baseService, loggerService)
        {
            _applicationSettingsService = applicationSettingsService;
        }

        #region UI APIs

        /// <summary>
        /// Retrieve a list of user profiles from the UserID
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///           "Component": "ProfilesList",
        ///           "TenantGuid": "{{TenantGuid}}",
        ///           "Language": "01",
        ///           "Controller": "PXW/AccountProfile",
        ///           "Action": "ProfilesList",
        ///           "PathParameters": {
        ///           },
        ///           "PayloadParameters": {
        ///           }
        ///      }
        /// </remarks>
        /// <response code="200">Returns the successful response of Profiles.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ProfilesList")]
        [SwaggerOperation("Retrieve a list of user profiles from the UserID.")]
        [Consumes("application/json")]
        [Produces("application/json")]
        [ProducesResponseType(typeof(ProfileResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesList()
        {
            try
            {
                // Log information about the request
                await _loggerService.LogRequestInformationAsync();
                _baseService.ReadRequestBody();

                ProfileEngine profileEngine = new ProfileEngine(_baseService.GetDomainId(), _baseService.GetLanguageId(),
                    _applicationSettingsService, _baseService.HttpContextAccessor);
                string? UserID = _baseService.HttpContextAccessor?.HttpContext?.Items["UserId"] as string;
                string? B2CToken = _baseService.HttpContextAccessor?.HttpContext?.Request.Headers[Constants.Authorization];
                B2CToken = B2CToken?.Substring("Bearer ".Length).Trim();
                ProfileResponse? profiles = null;
                if (UserID != null && B2CToken != null)
                {
                    bool withOrganization = true;
                    bool withAccounts = true;
                    if (_baseService.QueryParametersDictionary.TryGetValue("withOrganization", out string? withOrganizationValue))
                    {
                        withOrganization = withOrganizationValue.ToLower() == "true";
                    }
                    if (_baseService.QueryParametersDictionary.TryGetValue("withAccounts", out string? withAccountsValue))
                    {
                        withAccounts = withAccountsValue.ToLower() == "true";
                    }
                    profiles = await profileEngine.GetProfilesApi(UserID, B2CToken, false, withOrganization, withAccounts);
                }
                return Ok(profiles);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Retrieves the summary of member Id details from User Api.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///           "Component": "RetrieveMemberIdDetails",
        ///           "TenantGuid": "{{TenantGuid}}",
        ///           "Language": "01",
        ///           "Controller": "PXW/AccountProfile",
        ///           "Action": "RetrieveMemberIdDetails",
        ///           "PathParameters": {
        ///           },
        ///           "PayloadParameters": {
        ///           }
        ///      }
        /// </remarks>
        /// <response code="200">Returns the account summary data of the member.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("RetrieveMemberIdDetails")]
        [SwaggerOperation("Retrieve MemberId Details")]
        [Consumes("application/json")]
        [Produces("application/json")]
        [ProducesResponseType(typeof(MemberResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> RetrieveMemberIdDetails()
        {
            // Log information about the request
            await _loggerService.LogRequestInformationAsync();
            _baseService.ReadRequestBody();

            ProfileEngine profileEngine = new ProfileEngine(_baseService.GetDomainId(), _baseService.GetLanguageId(),
                _applicationSettingsService, _baseService.HttpContextAccessor);
            string? UserID = _baseService.HttpContextAccessor?.HttpContext?.Items["UserId"] as string;
            string? B2CToken = _baseService.HttpContextAccessor?.HttpContext?.Request.Headers[Constants.Authorization];
            B2CToken = B2CToken?.Substring("Bearer ".Length).Trim();

            MemberResponse? memberResponse = null;
            if (UserID != null && B2CToken != null)
            {
                memberResponse = profileEngine.GetMemberResponse(UserID, B2CToken);
            }
            return Ok(memberResponse);
        }

        /// <summary>
        /// Get list of accounts for a given profile id.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///           "Component": "AccountsList",
        ///           "TenantGuid": "{{TenantGuid}}",
        ///           "Language": "01",
        ///           "Controller": "PXW/AccountProfile",
        ///           "Action": "AccountsList",
        ///           "PathParameters": {
        ///             "profile_id": "{{ProfileId}}"
        ///           },
        ///           "PayloadParameters": {
        ///           }
        ///      }
        /// </remarks>
        /// <response code="200">Returns the list of accounts for a given profile id.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("AccountsList")]
        [SwaggerOperation("Get list of accounts for a given profile id")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(AccountResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> AccountsList()
        {
            try
            {
                // Log information about the request
                await _loggerService.LogRequestInformationAsync();
                _baseService.ReadRequestBody();

                ProfileEngine profileEngine = new ProfileEngine(_baseService.GetDomainId(), _baseService.GetLanguageId(),
                    _applicationSettingsService, _baseService.HttpContextAccessor);
                _baseService.PathParametersDictionary.TryGetValue("profile_id", out string? ProfileId);
                string? B2CToken = _baseService.HttpContextAccessor?.HttpContext?.Request.Headers[Constants.Authorization];
                B2CToken = B2CToken?.Substring("Bearer ".Length).Trim();
                AccountResponse? accounts = null;
                if (ProfileId != null && B2CToken != null)
                {
                    accounts = await profileEngine.GetAccountsApi(ProfileId, B2CToken);
                }
                return Ok(accounts);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Retrieve a list of Account profile from the AlphaccoKey."
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///           "Component": "GetProfileByAlphaCcoKey",
        ///           "TenantGuid": "{{TenantGuid}}",
        ///           "Language": "01",
        ///           "Controller": "PXW/AccountProfile",
        ///           "Action": "GetProfileByAlphaCcoKey",
        ///           "PathParameters": {
        ///             "AlphaCco_Key": "{{AlphaCcoKey}}"
        ///           },
        ///           "PayloadParameters": {
        ///           }
        ///      }
        /// </remarks>
        /// <response code="200">Returns the list of accounts for a given profile id.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetProfileByAlphaCcoKey")]
        [SwaggerOperation("Retrieve a list of user profile from the AlphaCCOKey.")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(AccountProfile), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetProfileByAlphaCcoKey()
        {
            // Log information about the request
            await _loggerService.LogRequestInformationAsync();
            _baseService.ReadRequestBody();

            ProfileEngine profileEngine = new ProfileEngine(_baseService.GetDomainId(), _baseService.GetLanguageId(),
                    _applicationSettingsService, _baseService.HttpContextAccessor);
            _baseService.PathParametersDictionary.TryGetValue("AlphaCco_Key", out string? alphaCCOKey);
            string? UserID = _baseService.HttpContextAccessor?.HttpContext?.Items["UserId"] as string;
            string? B2CToken = _baseService.HttpContextAccessor?.HttpContext?.Request.Headers[Constants.Authorization];
            B2CToken = B2CToken?.Substring("Bearer ".Length).Trim();

            AccountProfile? accountProfile = null;
            if (UserID != null && B2CToken != null)
            {
                AccountMember accountMember = profileEngine.GetAccountMember(UserID, B2CToken);
                accountProfile = _baseService.GetAccountProfileFromAccountMember(accountMember, alphaCCOKey);
            }
            return Ok(accountProfile);
        }

        /// <summary>
        /// Creates a private security.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///           "Component": "GetAccountMemberData",
        ///           "TenantGuid": "{{TenantGuid}}",
        ///           "Language": "01",
        ///           "Controller": "PXW/AccountProfile",
        ///           "Action": "GetAccountMemberData",
        ///           "PathParameters": {
        ///           },
        ///           "PayloadParameters": {
        ///           }
        ///      }
        /// </remarks>
        /// <response code="200">Returns the list of accounts for a given profile id.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetAccountMemberData")]
        [SwaggerOperation("Get Current Account Member details for all the profiles.")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(AccountMember), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetAccountMemberData()
        {
            try
            {
                // Log information about the request
                await _loggerService.LogRequestInformationAsync();
                _baseService.ReadRequestBody();

                ProfileEngine profileEngine = new ProfileEngine(_baseService.GetDomainId(), _baseService.GetLanguageId(),
                    _applicationSettingsService, _baseService.HttpContextAccessor);
                string? UserID = _baseService.HttpContextAccessor?.HttpContext?.Items["UserId"] as string;
                string? B2CToken = _baseService.HttpContextAccessor?.HttpContext?.Request.Headers[Constants.Authorization];
                B2CToken = B2CToken?.Substring("Bearer ".Length).Trim();
                AccountMember? accountMember = null;
                if (UserID != null && B2CToken != null)
                {
                    accountMember = profileEngine.GetAccountMember(UserID, B2CToken);
                }
                return Ok(accountMember);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Get Current Account Member Details for a selected Profile Id.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///           "Component": "GetAccountMemberProfileIdData",
        ///           "TenantGuid": "{{TenantGuid}}",
        ///           "Language": "01",
        ///           "Controller": "PXW/AccountProfile",
        ///           "Action": "GetAccountMemberProfileIdData",
        ///           "PathParameters": {
        ///           },
        ///           "PayloadParameters": {
        ///             "ProfileId": "{{profile_id}}"
        ///           }
        ///      }
        /// </remarks>
        /// <response code="200">Returns the list of accounts for a given profile id.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetAccountMemberProfileIdData")]
        [SwaggerOperation("Get Current Account Member Details for a selected Profile Id.")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(AccountMember), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetAccountMemberProfileIdData()
        {
            try
            {
                // Log information about the request
                await _loggerService.LogRequestInformationAsync();
                _baseService.ReadRequestBody();
                // Access payloadString from CommonService
                string payloadString = _baseService.PayloadString;
                CurrentUserRequest? request = _baseService.ConvertPayloadToObject<CurrentUserRequest>(payloadString);

                ProfileEngine profileEngine = new ProfileEngine(_baseService.GetDomainId(), _baseService.GetLanguageId(),
                    _applicationSettingsService, _baseService.HttpContextAccessor);
                string? UserID = _baseService.HttpContextAccessor?.HttpContext?.Items["UserId"] as string;
                string? B2CToken = _baseService.HttpContextAccessor?.HttpContext?.Request.Headers[Constants.Authorization];
                B2CToken = B2CToken?.Substring("Bearer ".Length).Trim();
                AccountMember? accountMember = null;
                if (UserID != null && B2CToken != null && request.ProfileId != null)
                {
                    accountMember = profileEngine.GetAccountMember(UserID, B2CToken, request.ProfileId);
                }
                return Ok(accountMember);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
        #endregion

        #region Interface


        ///// <summary>
        ///// Client request to create AccountMember object while AlphaCCO Key still unknown.
        ///// Client will pass in Email Address value and we will check if any AccountMember/AccountProfil with this email address already exist, if yes, return error for email address already exist
        ///// if not we create the Account member and set initial values
        ///// </summary>
        ///// <param name="emailaddress"></param>
        ///// <returns></returns>
        //[HttpPost]
        //[Route("api/accountprofile/CreateProfile")]
        //public IHttpActionResult CreateProfile(JObject contentRequest)
        //{
        //    ToolHelperResult publishResult = initToolHelerResult();
        //    APIResult retval = new APIResult();
        //    AccountProfile inpt = null;
        //    bool sendRegistrationEmail = false;
        //    if (contentRequest.Count == 2)
        //    {
        //        inpt = contentRequest.Children<JProperty>().Take(1).First().Value.ToObject<AccountProfile>();
        //        sendRegistrationEmail = contentRequest.Children<JProperty>().Skip(1).Take(1).First().Value.ToObject<bool>();


        //        //sendRegistrationEmail = contentRequest[1].ToObject<bool>();



        //        //inpt = contentRequest[0].ToObject<AccountProfile>();
        //        //sendRegistrationEmail = contentRequest[1].ToObject<bool>();
        //    }
        //    else

        //        inpt = contentRequest.ToObject<AccountProfile>();

        //    if (string.IsNullOrEmpty(inpt.Name))
        //        inpt.Name = inpt.FirstName + " " + inpt.LastName + " - " + inpt.AlphaCCOKey;

        //    AccountMember member = getAccountMember(string.Empty, inpt.Email);
        //    if (member == null)
        //    {
        //        publishResult = addMemberAccount(inpt, sendRegistrationEmail);
        //        retval.Status = publishResult.Result.GetDescription();
        //        retval.Message = publishResult.Message;
        //        return Ok(retval);
        //    }
        //    else
        //    {
        //        publishResult = updateMemberAccount(member, inpt, sendRegistrationEmail);
        //        retval.Status = publishResult.Result.GetDescription();
        //        retval.Message = publishResult.Message;
        //        return Ok(retval);

        //    }
        //}

        //[Route("api/accountprofile/UpdateProfile")]
        //[HttpPost]
        //public IHttpActionResult UpdateProfile(JObject contentRequest)
        //{
        //    //AccountProfile ap = new AccountProfile();
        //    //var test = JsonConvert.DeserializeAnonymousType(contentRequest.ToString(),ap );



        //    ToolHelperResult publishResult = initToolHelerResult();
        //    APIResult retval = new APIResult();
        //    AccountProfile inpt = null;
        //    bool sendRegistrationEmail = false;
        //    if (contentRequest.Count == 2)
        //    {
        //        inpt = contentRequest.Children<JProperty>().Take(1).First().Value.ToObject<AccountProfile>();
        //        sendRegistrationEmail = contentRequest.Children<JProperty>().Skip(1).Take(1).First().Value.ToObject<bool>();


        //        //sendRegistrationEmail = contentRequest[1].ToObject<bool>();



        //        //inpt = contentRequest[0].ToObject<AccountProfile>();
        //        //sendRegistrationEmail = contentRequest[1].ToObject<bool>();
        //    }
        //    else

        //        inpt = contentRequest.ToObject<AccountProfile>();

        //    if (string.IsNullOrEmpty(inpt.Name))
        //        inpt.Name = inpt.FirstName + " " + inpt.LastName + " - " + inpt.AlphaCCOKey;

        //    AccountMember member = getAccountMember(inpt.AlphaCCOKey, inpt.Email);
        //    if (member == null)
        //        publishResult = addMemberAccount(inpt, sendRegistrationEmail);
        //    else
        //        publishResult = updateMemberAccount(member, inpt, sendRegistrationEmail);



        //    //if (!string.IsNullOrEmpty(message))
        //    //{
        //    //    result.Success = false;
        //    //    result.Message = message;

        //    //}
        //    //else
        //    //{

        //    //    result.Success = true;
        //    //    result.Message = "Request submitted.";

        //    //    //Task.Run(() =>
        //    //    //{ DoWork("test"); }).ConfigureAwait(false);
        //    //}
        //    retval.Status = publishResult.Result.GetDescription();
        //    retval.Message = publishResult.Message;
        //    return Ok(retval);
        //}

        #endregion Interface
    }
}
