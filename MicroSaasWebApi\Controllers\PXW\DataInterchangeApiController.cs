using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using Swashbuckle.AspNetCore.Filters;

namespace MicroSaasWebApi.Controllers.PXW
{
    [Route("api/PXW/[controller]")]
    [ApiController]
    [EnableCors("AllowAll")]
    public class DataInterchangeApiController : BaseController
    {

        public DataInterchangeApiController(IBaseService baseService, ILoggerService loggerService) : base(baseService, loggerService)
        {
        }

        /// <summary>
        /// ListWorkflows
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "ListWorkflows",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "ListWorkflows"
        ///    }
        /// </remarks>
        /// <response code="200">Returns the list of workflows</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListWorkflows")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListWorkflows()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("ListWorkflows", true);
        }

        /// <summary>
        /// CreateWorkflow
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "CreateWorkflow",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "CreateWorkflow"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response when workflow is created</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateWorkflow")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateWorkflow()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("CreateWorkflow", true, "POST");
        }

        /// <summary>
        /// GetWorkflow
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetWorkflow",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "GetWorkflow"
        ///    }
        /// </remarks>
        /// <response code="200">Returns the workflow</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetWorkflow")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetWorkflow()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("GetWorkflow", true);
        }

        /// <summary>
        /// GetWorkflowSteps
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetWorkflowSteps",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "GetWorkflowSteps"
        ///    }
        /// </remarks>
        /// <response code="200">Returns the workflow steps</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetWorkflowSteps")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetWorkflowSteps()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("GetWorkflowSteps", true);
        }


        /// <summary>
        /// UpdateWorkflow
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "UpdateWorkflow",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "UpdateWorkflow"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response when workflow is updated</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UpdateWorkflow")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UpdateWorkflow()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("UpdateWorkflow", true, "PUT");
        }

        /// <summary>
        /// PartialUpdateWorkflow
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "PartialUpdateWorkflow",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "PartialUpdateWorkflow"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response when workflow is updated</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("PartialUpdateWorkflow")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> PartialUpdateWorkflow()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("UpdateWorkflow", true, "PATCH");
        }

        /// <summary>
        /// DeleteWorkflow
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "DeleteWorkflow",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "DeleteWorkflow"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response when workflow is deleted</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DeleteWorkflow")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DeleteWorkflow()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("DeleteWorkflow", true, "DELETE");
        }

        /// <summary>
        /// ActivateWorkflow
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "ActivateWorkflow",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "ActivateWorkflow"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response when workflow is activated</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ActivateWorkflow")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ActivateWorkflow()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("ActivateWorkflow", true, "PUT");
        }

        /// <summary>
        /// ArchiveWorkflow
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "ArchiveWorkflow",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "ArchiveWorkflow"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response when workflow is archived</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ArchiveWorkflow")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ArchiveWorkflow()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("ArchiveWorkflow", true, "PUT");
        }

        /// <summary>
        /// ListWorkflowVersions
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "ListWorkflowVersions",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "ListWorkflowVersions"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a list of workflow versions</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListWorkflowVersions")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListWorkflowVersions()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("ListWorkflowVersions", true);
        }

        /// <summary>
        /// GetWorkflowPreview
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetWorkflowPreview",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "GetWorkflowPreview"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a workflow preview</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetWorkflowPreview")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetWorkflowPreview()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("GetWorkflowPreview", true);
        }

        /// <summary>
        /// GetWorkflowVersionPreview
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetWorkflowVersionPreview",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "GetWorkflowVersionPreview"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a workflow version preview</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetWorkflowVersionPreview")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetWorkflowVersionPreview()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("GetWorkflowVersionPreview", true);
        }

        /// <summary>
        /// SetWorkflowPublicVersion
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "SetWorkflowPublicVersion",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "SetWorkflowPublicVersion"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response when workflow public version is set</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("SetWorkflowPublicVersion")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> SetWorkflowPublicVersion()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("SetWorkflowPublicVersion", true, "PUT");
        }

        /// <summary>
        /// UploadWorkflowVersion
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "UploadWorkflowVersion",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "UploadWorkflowVersion"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response when workflow is uploaded</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UploadWorkflowVersion")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UploadWorkflowVersion()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("UploadWorkflowVersion", true, "PUT");
        }

        /// <summary>
        /// GetWorkflowYAML
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetWorkflowYAML",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "GetWorkflowYAML"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response with Workflow YAML</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetWorkflowYAML")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetWorkflowYAML()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("GetWorkflowYAML", true);
        }

        /// <summary>
        /// GetWorkflowVersionYAML
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetWorkflowVersionYAML",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "GetWorkflowVersionYAML"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response with Workflow YAML</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetWorkflowVersionYAML")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetWorkflowVersionYAML()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("GetWorkflowVersionYAML", true);
        }

        /// <summary>
        /// ListWorkflowInstances
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "ListWorkflowInstances",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "ListWorkflowInstances"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a list of workflow instances</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListWorkflowInstances")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListWorkflowInstances()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("ListWorkflowInstances", true);
        }

        /// <summary>
        /// CreateWorkflowInstance
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "CreateWorkflowInstance",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "CreateWorkflowInstance"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response when workflow instance is created</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateWorkflowInstance")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateWorkflowInstance()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("CreateWorkflowInstance", true, "POST");
        }

        /// <summary>
        /// GetWorkflowInstance
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetWorkflowInstance",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "GetWorkflowInstance"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a workflow instance</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetWorkflowInstance")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetWorkflowInstance()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("GetWorkflowInstance", true);
        }

        /// <summary>
        /// UpdateWorkflowInstance
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "UpdateWorkflowInstance",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "UpdateWorkflowInstance"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response when workflow instance is updated</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UpdateWorkflowInstance")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UpdateWorkflowInstance()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("UpdateWorkflowInstance", true, "PUT");
        }

        /// <summary>
        /// PartialUpdateWorkflowInstance
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "PartialUpdateWorkflowInstance",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "PartialUpdateWorkflowInstance"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response when workflow instance is updated</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("PartialUpdateWorkflowInstance")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> PartialUpdateWorkflowInstance()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("UpdateWorkflowInstance", true, "POST");
        }

        /// <summary>
        /// GetWorkflowInstanceLogs
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetWorkflowInstanceLogs",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "GetWorkflowInstanceLogs"
        ///    }
        /// </remarks>
        /// <response code="200">Returns the logs of a workflow instance</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetWorkflowInstanceLogs")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetWorkflowInstanceLogs()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("GetWorkflowInstanceLogs", true);
        }

        /// <summary>
        /// GetWorkflowInstanceComments
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetWorkflowInstanceComments",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "GetWorkflowInstanceComments"
        ///    }
        /// </remarks>
        /// <response code="200">Returns the comments of a workflow instance</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetWorkflowInstanceComments")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetWorkflowInstanceComments()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("GetWorkflowInstanceComments", true);
        }

        /// <summary>
        /// CreateWorkflowInstanceComment
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "CreateWorkflowInstanceComment",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "CreateWorkflowInstanceComment"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response when workflow instance comment is created</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateWorkflowInstanceComment")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateWorkflowInstanceComment()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("CreateWorkflowInstanceComment", true, "POST");
        }

        /// <summary>
        /// GetWorkflowInstanceData
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetWorkflowInstanceData",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "GetWorkflowInstanceData"
        ///    }
        /// </remarks>
        /// <response code="200">Returns the data of a workflow instance</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetWorkflowInstanceData")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetWorkflowInstanceData()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("GetWorkflowInstanceData", true);
        }

        /// <summary>
        /// CreateWorkflowInstanceData form request data.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST {{domain}}/api
        ///    {
        ///        "Component": "CreateWorkflowInstanceData",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "CreateWorkflowInstanceData"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response when workflow instance data is created</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateWorkflowInstanceData")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateWorkflowInstanceData()
        {
            return await _baseService.FetchApiEndpointsWithBearerToken("CreateWorkflowInstanceData", true, "POST");
        }

        /// <summary>
        /// UpdateWorkflowInstanceData form request data.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST {{domain}}/api
        ///    {
        ///        "Component": "UpdateWorkflowInstanceData",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "UpdateWorkflowInstanceData"
        ///    }
        /// </remarks>
        /// <response code="200">Returns a successful response when workflow instance data is updated</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UpdateWorkflowInstanceData")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UpdateWorkflowInstanceData()
        {
            return await _baseService.FetchApiEndpointsWithBearerToken("UpdateWorkflowInstanceData", true, "POST");
        }

        /// <summary>
        /// GetWorkflowInstanceQuestions
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetWorkflowInstanceQuestions",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "GetWorkflowInstanceQuestions"
        ///    }
        /// </remarks>
        /// <response code="200">Returns the questions of a workflow instance</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetWorkflowInstanceQuestions")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetWorkflowInstanceQuestions()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("GetWorkflowInstanceQuestions", true);
        }

        /// <summary>
        /// GetWorkflowInstanceSteps
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetWorkflowInstanceSteps",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "GetWorkflowInstanceSteps"
        ///    }
        /// </remarks>
        /// <response code="200">Returns the steps of a workflow instance</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetWorkflowInstanceSteps")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetWorkflowInstanceSteps()
        {
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("GetWorkflowInstanceSteps", true);
        }

        /// <summary>
        /// AssignWorkflowInstance
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "AssignWorkflowInstance",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "AssignWorkflowInstance"
        ///    }
        /// </remarks>
        /// <response code="200">Returns the steps of a workflow instance</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("AssignWorkflowInstance")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> AssignWorkflowInstance()
        {
            await _loggerService.LogInformationAsync("AssignWorkflowInstance");
            return await _baseService.FetchApiEndpointsDataInterchangeWithOptionalToken("AssignWorkflowInstance", true, "POST");
        }

        /// <summary>
        /// GetWorkflowInstanceFile
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetWorkflowInstanceFile",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DataInterchangeApi",
        ///        "Action": "GetWorkflowInstanceFile",
        ///        "PathParameters": {
        ///            "instance_id": "{{instance_id}}",
        ///            "datatag": "DriverLicense"
        ///        }
        ///    }
        /// </remarks>
        /// <response code="200">Returns the data of a workflow instance</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetWorkflowInstanceFile")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetWorkflowInstanceFile()
        {
            return await _baseService.FetchApiEndpointsWithBearerToken("GetWorkflowInstanceFile", true);
        }
    }
}