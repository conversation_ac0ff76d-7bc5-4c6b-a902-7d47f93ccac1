﻿using MicroSaasWebApi.Properties.Enums.PXW;
using System.ComponentModel;
using System.Globalization;
using System.Security.Cryptography;
using System.Text.RegularExpressions;

namespace MicroSaasWebApi.Properties.PXW
{
    public static class ApiHelper
    {
        public static DateTime GetDate(string datestring) //yyyymmdd
        {
            DateTime rtn = DateTime.Now.Date;
            try
            {
                rtn = new DateTime(Convert.ToInt32(datestring.Substring(0, 4)),
                    Convert.ToInt32(datestring.Substring(4, 2)), Convert.ToInt32(datestring.Substring(6)));
            }
            catch
            {

            }
            return rtn;

        }
        public static double? ConvertToDouble(string value, double? defaultResult = null)
        {
            if (Double.TryParse(value, out double result)) return Convert.ToDouble(value);
            return defaultResult;
        }

        public static string DecryptString(string key, string cipherText)
        {
            // Convert UUID to byte array
            var cleanKey = key.Replace("-", "");
            var keyBytes = new byte[cleanKey.Length / 2];

            for (var i = 0; i < keyBytes.Length; i++)
            {
                keyBytes[i] = Convert.ToByte(cleanKey.Substring(i * 2, 2), 16);
            }

            // Resize the key if necessary. AES key length must be 16 bytes (128 bits), 24 bytes (192 bits) or 32 bytes (256 bits).
            Array.Resize(ref keyBytes, 32);

            var allBytes = Convert.FromBase64String(cipherText);
            var iv = new byte[16];
            Array.Copy(allBytes, 0, iv, 0, iv.Length);

            var cipherTextBytes = new byte[allBytes.Length - iv.Length];
            Array.Copy(allBytes, iv.Length, cipherTextBytes, 0, cipherTextBytes.Length);

            using (var aes = Aes.Create())
            {
                aes.Key = keyBytes;
                aes.IV = iv; // Use the extracted IV
                var decryptor = aes.CreateDecryptor();

                using (var ms = new MemoryStream(cipherTextBytes))
                {
                    using (var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                    {
                        using (var sr = new StreamReader(cs))
                        {
                            return sr.ReadToEnd();
                        }
                    }
                }
            }
        }

        public static string EncryptString(string key, string plainText)
        {
            // Convert UUID to byte array
            var cleanKey = key.Replace("-", "");
            var keyBytes = new byte[cleanKey.Length / 2];

            for (var i = 0; i < keyBytes.Length; i++)
            {
                keyBytes[i] = Convert.ToByte(cleanKey.Substring(i * 2, 2), 16);
            }

            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);

            using (var aes = Aes.Create())
            {
                // Resize the key if necessary. AES key length must be 16 bytes (128 bits), 24 bytes (192 bits) or 32 bytes (256 bits).
                Array.Resize(ref keyBytes, 32);
                aes.Key = keyBytes;

                var encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                using (var ms = new MemoryStream())
                {
                    using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                    {
                        cs.Write(plainTextBytes, 0, plainTextBytes.Length);
                    }

                    var cipherTextBytes = ms.ToArray();

                    // Combine the IV and cipher text
                    var combinedBytes = new byte[aes.IV.Length + cipherTextBytes.Length];
                    Array.Copy(aes.IV, 0, combinedBytes, 0, aes.IV.Length);
                    Array.Copy(cipherTextBytes, 0, combinedBytes, aes.IV.Length, cipherTextBytes.Length);

                    return Convert.ToBase64String(combinedBytes);
                }
            }
        }

        public static string GetDescription<T>(this T e) where T : IConvertible
        {
            if (e is Enum)
            {
                Type type = e.GetType();
                Array values = System.Enum.GetValues(type);

                foreach (int val in values)
                {
                    if (val == e.ToInt32(CultureInfo.InvariantCulture))
                    {
                        var memInfo = type.GetMember(type.GetEnumName(val));
                        var descriptionAttribute = memInfo[0]
                            .GetCustomAttributes(typeof(DescriptionAttribute), false)
                            .FirstOrDefault() as DescriptionAttribute;

                        if (descriptionAttribute != null)
                        {
                            return descriptionAttribute.Description;
                        }
                    }
                }
            }

            return string.Empty;
        }

        public static int GetLanguageId(string lang)
        {
            if (Enum.TryParse<Language>(lang, true, out var result))  // case-insensitive parse
            {
                return (int)result;
            }
            else
            {
                throw new ArgumentException($"Invalid language: {lang}", nameof(lang));
            }
        }

        // Helper method to check if the content is in JSON format
        static bool IsJsonContent(string content)
        {
            try
            {
                _ = Newtonsoft.Json.Linq.JToken.Parse(content);
                return true;
            }
            catch
            {
                return false;
            }
        }
        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                // Normalize the domain
                email = Regex.Replace(email, @"(@)(.+)$", DomainMapper,
                    RegexOptions.None, TimeSpan.FromMilliseconds(200));

                // Examines the domain part of the email and normalizes it.
            }
            catch (RegexMatchTimeoutException)
            {
                return false;
            }
            catch (ArgumentException)
            {
                return false;
            }

            try
            {
                return Regex.IsMatch(email,
                    @"^(?("")("".+?(?<!\\)""@)|(([0-9a-z]((\.(?!\.))|[-!#\$%&'\*\+/=\?\^`\{\}\|~\w])*)(?<=[0-9a-z])@))" +
                    @"(?(\[)(\[(\d{1,3}\.){3}\d{1,3}\])|(([0-9a-z][-0-9a-z]*[0-9a-z]*\.)+[a-z0-9][\-a-z0-9]{0,22}[a-z0-9]))$",
                    RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            }
            catch (RegexMatchTimeoutException)
            {
                return false;
            }
        }
        private static string DomainMapper
            (Match match)
        {
            // Use IdnMapping class to convert Unicode domain names.
            var idn = new IdnMapping();

            // Pull out and process domain name (throws ArgumentException on invalid)
            var domainName = idn.GetAscii(match.Groups[2].Value);

            return match.Groups[1].Value + domainName;
        }

        // Helper method to unescape the filename
        public static string UnescapeFilename(string? filename)
        {
            return filename?.Replace("\"", "") ?? string.Empty;
        }

        public static async Task<string> GetRequestDetailsAsync(HttpRequestMessage request)
        {
            var requestInfo = new System.Text.StringBuilder();

            // Log Request URI and Method
            requestInfo.AppendLine($"Method: {request.Method}");
            requestInfo.AppendLine($"Request URI: {request.RequestUri}");

            // Log Headers
            requestInfo.AppendLine("Headers:");
            foreach (var header in request.Headers)
            {
                requestInfo.AppendLine($"{header.Key}: {string.Join(", ", header.Value)}");
            }

            // Log Content (if available)
            if (request.Content != null)
            {
                requestInfo.AppendLine("Content Headers:");
                foreach (var header in request.Content.Headers)
                {
                    requestInfo.AppendLine($"{header.Key}: {string.Join(", ", header.Value)}");
                }

                var content = await request.Content.ReadAsStringAsync();
                requestInfo.AppendLine($"Body: {content}");
            }
            return requestInfo.ToString();
        }
    }
}
