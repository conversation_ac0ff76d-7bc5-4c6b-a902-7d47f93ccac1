extends:
  template: ../../azure_pipeline_template.yml
  parameters:
    environment: PROD
    client: PXW
    dockerRegistryServiceConnection: "************************************"
    imageRepository: "pxw-webapi-prod"
    containerRegistry: "prodpxwacr.azurecr.io"
    dockerfilePath: "$(Build.SourcesDirectory)/Dockerfile"
    tag: "PXW-PROD-$(Build.BuildId)"
    poolName: "terraform-pool"
    azureSubscription: "PXW-PROD(f88e9b38-87cc-476a-b1df-dccd1cfb968b)"
    webAppName: "pxw-webapi-prod"
    appSettingsEnvironment: "-ASPNETCORE_ENVIRONMENT Staging"
