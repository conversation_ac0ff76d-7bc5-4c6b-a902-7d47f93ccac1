﻿using TuesPechkin;

namespace PXW
{
    /// <summary>
    /// Summary description for PDFHelper
    /// </summary>
    public static class PDFHelper
    {
        private static IConverter _converter = null;

        public static IConverter converter
        {
            get
            {
                if (_converter == null)
                {
                    _converter =
            new ThreadSafeConverter(
                new RemotingToolset<PdfToolset>(
                    new WinAnyCPUEmbeddedDeployment(
                        new TempFolderDeployment())));
                }
                return _converter;
            }
        }

        public static byte[] GetFile(HtmlToPdfDocument document)
        {


            try
            {
                byte[] result = converter.Convert(document);
                return result;
            }
            catch { return null; }

        }
    }

}