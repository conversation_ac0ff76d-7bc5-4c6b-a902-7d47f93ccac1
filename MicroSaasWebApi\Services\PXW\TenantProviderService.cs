﻿using MicroSaasWebApi.Services.PXW.Interface;

namespace MicroSaasWebApi.Services.PXW
{
    public class TenantProviderService : ITenantProviderService
    {
        private readonly IConfiguration _configuration;
        private string _tenantId;

        public TenantProviderService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public void SetTenantId(string tenantId)
        {
            _tenantId = tenantId;
        }
    }
}
