﻿using System.Net.Security;
using System.Security.Cryptography.X509Certificates;

namespace DocumentBatchProcess
{
    internal class Program
    {
        static async Task Main(string[] args)
        {
            Task[] tasks = new Task[1];

            tasks[0] = Task.Run(() => MoveDocAndSentEmail());


            Task.WaitAll(tasks);

            Console.WriteLine("All tasks completed.");

        }

        private static async Task MoveDocAndSentEmail()
        {
            using (HttpClient client = new HttpClient())
            {
                try
                {
                    //string token1 = Encryption.RijndaelEncryption.Encrypt(DateTime.Now.ToUniversalTime().ToString(), ConfigurationManager.AppSettings["APIKey"].ToString());
                    var config = ConfigLoader.Load();
                    //string token = ConfigurationManager.AppSettings["APIKey"].ToString();
                    string token = config.GetSection("MySettings")["APIKey"];

                    var request = new HttpRequestMessage(HttpMethod.Post, config.GetSection("MySettings")["BatchProcessEndPoint"].ToString());
                    request.Headers.Add("Authorization", "Bearer " + config.GetSection("MySettings")["APIKey"]);
                    var para = new MultipartFormDataContent();
                    para.Add(new StringContent(token), "token");
                    request.Content = para;

                    var response = await client.SendAsync(request);

                    if (response.IsSuccessStatusCode)
                    {
                        string content = await response.Content.ReadAsStringAsync();
                        Console.WriteLine("Received response: {0}", content);
                    }
                    else
                    {
                        Console.WriteLine("Error, received status code {0}: {1}", response.StatusCode, response.ReasonPhrase);
                    }
                }
                catch (Exception ex)
                {
                    string s = ex.Message;
                    Console.WriteLine("Error, Message is {0}", ex.Message);
                }

            }

        }

        public static bool ValidateServerCertificate(
              object sender,
              X509Certificate certificate,
              X509Chain chain,
              SslPolicyErrors sslPolicyErrors)
        {
            Console.WriteLine("Validating certificate {0}", certificate.Issuer);
            if (sslPolicyErrors == SslPolicyErrors.None)
                return true;

            Console.WriteLine("Certificate error: {0}", sslPolicyErrors);

            // Do not allow this client to communicate with unauthenticated servers.
            return false;
        }

        internal class Member
        {
            public string Name { get; set; }
            public int Age { get; set; }
        }
    }

}
