﻿using MicroSaasWebApi.Properties.Enums;

namespace MicroSaasWebApi.Models.AlphaCCO
{
    public class SteppedTileCard
    {
        public SteppedTile SteppedTile { get; set; }

        public string StepURL
        {
            get
            {
                if (SteppedTile != null)
                {
                    return SteppedTile.StepURL;
                }
                else
                {
                    return string.Empty;
                }
            }
        }

        public string StepStatusClassName
        {
            get
            {
                if (SteppedTile != null)
                {
                    return SteppedTile.StepStatusClassName;
                }
                else
                {
                    return string.Empty;
                }
            }
        }
        public string StepStatusName
        {
            get
            {
                if (SteppedTile != null)
                {
                    return string.IsNullOrEmpty(SteppedTile.StepStatusName) ? string.Empty : SteppedTile.StepStatusName.ToString();
                }
                else
                {
                    return string.Empty;
                }
            }
        }

        public string StepStatusIcon
        {
            get
            {
                if (StepStatusName == eWorkflowStatus.Completed.ToString())
                    return "fa-check-circle";
                else
                    return "fa-info-circle";
            }
        }
        public string StepIconURL
        {
            get
            {
                if (SteppedTile != null)
                {
                    if (string.IsNullOrEmpty(SteppedTile.StepIconURL))
                        return "/PXW/Assets/Images/WorkflowIcons/Blue/PHW-Icon-Blue-4.png";
                    return SteppedTile.StepIconURL;
                }
                else
                {
                    return string.Empty;
                }
            }
        }
        public string WorkflowName
        {
            get
            {
                if (SteppedTile != null)
                {
                    return SteppedTile.WorkflowName;
                }
                else
                {
                    return string.Empty;
                }
            }
        }
        public string WorkflowDesc
        {
            get
            {
                if (SteppedTile != null)
                {
                    return SteppedTile.WorkflowDesc;
                }
                else
                {
                    return string.Empty;
                }
            }
        }
        public string WorkflowButtonText
        {
            get
            {
                if (SteppedTile != null)
                {
                    return SteppedTile.WorkflowButtonText;
                }
                else
                {
                    return string.Empty;
                }
            }
        }
    }
}
