extends:
  template: ../../azure_pipeline_template.yml
  parameters:
    environment: PROD
    client: WEBAPI
    dockerRegistryServiceConnection: "************************************"
    imageRepository: "webapi-webapi-prod"
    containerRegistry: "prodwebapiiacr.azurecr.io"
    dockerfilePath: "$(Build.SourcesDirectory)/Dockerfile"
    tag: "WEBAPI-PROD-$(Build.BuildId)"
    poolName: "terraform-pool"
    azureSubscription: "WEBAPI-PROD(f88e9b38-87cc-476a-b1df-dccd1cfb968b)"
    webAppName: "webapi-webapi-prod"
    appSettingsEnvironment: "-ASPNETCORE_ENVIRONMENT Staging"
