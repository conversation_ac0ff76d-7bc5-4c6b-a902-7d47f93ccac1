﻿namespace MicroSaasWebApi.Models.AlphaCCO
{
    /// <summary>
    /// Summary description for AlphaCCOWorkflow
    /// </summary>
    /// 
    public class AlphaCCOWorkflow
    {
        public string? WorkflowKey { get; set; }
        public string? AlphaCCOStatusCode { get; set; }
        public string? Name { get; set; }
        public string? ShortDescription { get; set; }
        public string? Description { get; set; }
        public DateTime PublishDate { get; set; }
        public int AlphaCCOVersionNumber { get; set; }
        public bool IsInMenu { get; set; }
        public bool IsMandatory { get; set; }
        public bool IsAutoApproval { get; set; } = true;  //Used for workflow engine to know if a next workflow 
        public bool IsSingleInstance { get; set; }  //Can be used to let front end to check on existing workflow before start a new one      
        public bool IsNeedPreviousWorkflowInstanceID { get; set; }  // to include the most recent completed same workflow instanceID in the Start New call.
        public bool IsNeedParentWorkflowInstanceID { get; set; }  //for AlphaCCO request to include parent workflow instance ID
        public Guid RequiredParentWorkflowID { get; set; } //for Enginess to know this workflow have dependency
        public Guid WorkflowIDtoSearchOnCompletedStatus { get; set; } //client want to pass in this most recent compleated workflow instanceId.
        public bool IsIndividualWorkflow { get; set; }

        /// <summary>
        /// new added on April 4, 2023, change logic for load latest prevoius completed workflows for POAC
        /// </summary>
        public bool IsPOACWorkflow { get; set; }
        public List<string>? WorkflowIDstoSearchOnCompletedStatus { get; set; }
        //public AdvantageImage ActiveIcon { get; set; }
        //public AdvantageImage InActiveIcon { get; set; }
        //public AdvantageImage PendingIcon { get; set; }
        //public AdvantageImage CompletedIcon { get; set; }

        public string? TitleForDisabled { get; set; }
        public string? TitleForCompleted { get; set; }
        public string? TitleForContinueNext { get; set; }
        public string? TitleForPending { get; set; }
        public string? TitleForNotStarted { get; set; }

        public string? WorkflowButtonText { get; set; }
        public string? WorkflowInProcessingButtonText { get; set; }
        public Guid MasterID { get; set; }
        public string? workflowId { get; set; }
        public string? workflowInstanceId { get; set; }

        //protected override void SetSummaryDataRow()
        //{
        //    AddSummaryDataRow("Display", Name);
        //    AddSummaryDataRow("Mandatory", IsMandatory);
        //    AddSummaryDataRow("AlphaCCOStatusCode", AlphaCCOStatusCode);
        //}

        //protected override void SetSearchableProperties()
        //{
        //    AddSearchableProperty("AlphaCCOKey", AlphaCCOKey);
        //    AddSearchableProperty("AlphaCCOStatusCode", AlphaCCOStatusCode);
        //    AddSearchableProperty("Name", Name);
        //    AddSearchableProperty("RequiredParentWorkflowID", RequiredParentWorkflowID);
        //}

    }
}
