using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using Swashbuckle.AspNetCore.Filters;

namespace MicroSaasWebApi.Controllers.PXW
{
    [Route("api/PXW/[controller]")]
    [ApiController]
    [EnableCors("AllowAll")]
    public class DocumentApiController : BaseController
    {
        public DocumentApiController(IBaseService baseService, ILoggerService loggerService) : base(baseService, loggerService)
        {
        }

        /// <summary>
        /// Retrieves the list of documents
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///        "Component": "ListDocuments",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "ListDocuments",
        ///        "QueryParameters": {
        ///            "path" : "pxw-test-cell/*",
        ///            "limit": "25",
        ///            "offset": "0"
        ///        }
        ///    }
        ///
        /// </remarks>
        /// <response code="200">Returns the list of documents</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListDocuments")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListDocuments()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("ListDocuments");
            return res;
        }

        /// <summary>
        /// search documents
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Details": true,
        ///         "From": 0,
        ///         "Size": 10,
        ///         "Query": {
        ///             // "Content": "string",
        ///             // "Extension": "txt",
        ///             "FileName": "NANI.txt",
        ///             // "FileNameOrContent": "NANI",
        ///             // "DocumentCategory": "doc category hihi",
        ///             // "RelatedAccount": "TEST_ACCOUNT",
        ///             "PathPrefix": [
        ///                 "pxw-test-cell"
        ///             ]
        ///             // "Not": false
        ///             // "Paths": [
        ///             //     "pxw-test-cell/2021/*"
        ///             // ]
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the list of documents</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("SearchDocuments")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> SearchDocuments()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("SearchDocuments", "POST");
            return res;
        }

        /// <summary>
        /// download list of documents
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///        "Component": "DownloadDocuments",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "DownloadDocuments",
        ///        "PayloadParameters": {
        ///            "file_action": "download",
        ///            "paths": [
        ///                "pxw-test-cell/meta_2020_subset.pdf"
        ///            ]
        ///        }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns documents</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DownloadDocuments")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DownloadDocuments()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("DownloadDocuments", "POST");
            return res;
        }

        /// <summary>
        /// upload list of documents
        /// </summary>
        /// <remarks>
        /// This request is uses a FormData request body.
        /// Here are the keys of the form data:
        ///
        ///     POST
        ///     {
        ///       file_action: upload
        ///       documents: list of files to upload
        ///       path: path to upload to
        ///       TenantGuid: tenant guid
        ///       Controller: PXW/DocumentApi
        ///       Action: UploadDocuments
        ///       Component: UploadDocuments
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns success or failure</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UploadDocuments")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UploadDocuments()
        {
            await _loggerService.LogInformationAsync("IN UPLOAD DOCUMENTS");
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("UploadDocuments", "POST");
            return res;
        }

        /// <summary>
        /// delete the list of documents
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///        "Component": "DeleteDocuments",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "DeleteDocuments",
        ///        "BodyParameters": {
        ///            "file_action" : "delete",
        ///            "nodes": "['folder/file1.txt']",
        ///            "remove_permanently": true
        ///        }
        ///    }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DeleteDocuments")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DeleteDocuments()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("DeleteDocuments", "POST");
            return res;
        }

        /// <summary>
        /// move or copy a list of documents
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT
        ///     {
        ///        "Component": "MoveDocuments",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "MoveDocuments",
        ///        "BodyParameters": {
        ///            "action_type" : "copy or move",
        ///            "paths": "['folder/folder2/file1.txt']",
        ///            "target": "folder/folder1"
        ///        }
        ///    }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("MoveDocuments")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> MoveDocuments()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("MoveDocuments", "PUT");
            return res;
        }

        /// <summary>
        /// Create or Update a role
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT
        ///     {
        ///       "action": "create",
        ///       "ForceOverride": true,
        ///       "Uuid": "testtest",
        ///       "Label": "This is a Test Role",
        ///       "Policies": [
        ///         {
        ///             "Action": "READ",
        ///             "Subject": "profile:standard",
        ///             "Effect": "allow"
        ///         },
        ///         {
        ///             "Action": "WRITE",
        ///             "Subject": "profile:admin",
        ///             "Effect": "allow"
        ///         },
        ///         {
        ///             "Action": "WRITE",
        ///             "Subject": "user:YOUR_USER",
        ///             "Effect": "allow"
        ///         }
        ///       ]
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateRole")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateRole()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("CreateRole", "POST");
            return res;
        }

        /// <summary>
        /// list roles
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT
        ///     {
        ///       "Limit": 25,
        ///       "Offset": 0,
        ///       "Operation": "AND",
        ///       "Queries": [
        ///           {
        ///               "Uuid": [
        ///                   "testtest"
        ///               ]
        ///           }
        ///       ]
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListRoles")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListRoles()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("ListRoles", "POST");
            return res;
        }

        /// <summary>
        /// Delete a role
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     DELETE
        ///     {
        ///       "role_id": "role_id_to_delete"
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DeleteRole")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DeleteRole()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("DeleteRole", "DELETE");
            return res;
        }

        /// <summary>
        /// get role
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET
        ///     {
        ///       "role_id": "role_id_we_want"
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetRole")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetRole()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("GetRole", "GET");
            return res;
        }

        /// <summary>
        /// create a folder at a specific path, can recursively create the folder
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "CreateFolder",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "CreateFolder",
        ///        "BodyParameters": {
        ///            "file_action": "create_folder",
        ///            "path": "folder/folder2"
        ///        }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateFolder")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateFolder()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("CreateFolder", "POST");
            return res;
        }

        /// <summary>
        /// Activate an External User
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "ActivateExternalUser",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DocumentApi",
        ///         "Action": "ActivateExterrnalUser",
        ///         "QueryParameters": {
        ///             "profileName": "John Doe",
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ActivateExternalUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ActivateExternalUser()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("ActivateExternalUser", "GET");
            return res;
        }

        /// <summary>
        /// Create an External User
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "CreateExternalUser",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DocumentApi",
        ///         "Action": "CreateExterrnalUser",
        ///         "PayloadParameters": {
        ///             "first_name": "John",
        ///             "last_name": "Doe",
        ///             "email": "<EMAIL>"
        ///             "profile_name": "John Doe",
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateExternalUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateExternalUser()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("CreateExternalUser", "POST");
            return res;
        }

        /// <summary>
        /// Deactivate an External User
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "DeactivateExternalUser",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DocumentApi",
        ///         "Action": "DeactivateExterrnalUser",
        ///         "QueryParameters": {
        ///             "profileName": "John Doe",
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DeactivateExternalUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DeactivateExternalUser()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("DeactivateExternalUser", "GET");
            return res;
        }

        /// <summary>
        /// Create an External User
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "InviteExternalUser",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DocumentApi",
        ///         "Action": "InviteExterrnalUser",
        ///         "QueryParameters": {
        ///             "profileName": "john Doe"
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("InviteExternalUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> InviteExternalUser()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("InviteExternalUser", "GET");
            return res;
        }

        /// <summary>
        /// List my organizations
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "ListOrganizations",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DocumentApi",
        ///         "Action": "ListOrganizations",
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListOrganizations")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListOrganizations()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("ListOrganizations", "GET");
            return res;
        }

        /// <summary>
        /// Create a User
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "CreateUser",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DocumentApi",
        ///         "Action": "CreateUser",
        ///         "PayloadParameters": {
        ///             "username": "testuser",
        ///             "group_path": "/users",
        ///             "profile": "standard",
        ///             "name": "Johnny Cadillac V2",
        ///             "email": "<EMAIL>"
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateUser()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("CreateUser", "POST");
            return res;
        }

        /// <summary>
        /// Update a User
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "UpdateUser",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DocumentApi",
        ///         "Action": "UpdateUser",
        ///         "PayloadParameters": {
        ///             "username": "testuser",
        ///             "name": "Johnny Cadillacwwwww",
        ///             "profile": "standard"
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UpdateUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UpdateUser()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("UpdateUser", "PATCH");
            return res;
        }

        /// <summary>
        /// get user
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "GetUser",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DocumentApi",
        ///         "Action": "GetUser",
        ///         "PathParameters": {
        ///             "user_id": "testuser"
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetUser()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("GetUser", "GET");
            return res;
        }

        /// <summary>
        /// Delete a user
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "DeleteUser",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DocumentApi",
        ///         "Action": "DeleteUser",
        ///         "PathParameters": {
        ///             "user_id": "testuser"
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DeleteUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DeleteUser()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("DeleteUser", "DELETE");
            return res;
        }

        /// <summary>
        /// Create a Group
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "CreateGroup",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DocumentApi",
        ///         "Action": "CreateGroup",
        ///         "PayloadParameters": {
        ///             "group_name": "whazza"
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateGroup")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateGroup()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("CreateGroup", "POST");
            return res;
        }

        /// <summary>
        /// Update a group
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "UpdateGroup",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DocumentApi",
        ///         "Action": "UpdateGroup",
        ///         "PayloadParameters": {
        ///             "current_group_name": "whazza",
        ///             "new_group_name": "houhou"
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UpdateGroup")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UpdateGroup()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("UpdateGroup", "PATCH");
            return res;
        }

        /// <summary>
        /// get group
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "GetGroup",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DocumentApi",
        ///         "Action": "GetGroup",
        ///         "PathParameters": {
        ///             "group_id": "houhou"
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetGroup")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetGroup()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("GetGroup", "GET");
            return res;
        }

        /// <summary>
        /// Delete a group
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "DeleteGroup",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DocumentApi",
        ///         "Action": "DeleteGroup",
        ///         "PathParameters": {
        ///             "group_id": "houhou"
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DeleteGroup")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DeleteGroup()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("DeleteGroup", "DELETE");
            return res;
        }

        /// <summary>
        /// Get document categories available for selection
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "DocumentCategoryDropDown",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "GetDocumentCategories"
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetDocumentCategories")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetDocumentCategories()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("GetDocumentCategories", "GET");
            return res;
        }

        /// <summary>
        /// Get User Permissions
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetUserPermissions",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "GetUserPermissions",
        ///        "PathParameters": {
        ///            "user_id": "testuser"
        ///         }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetUserPermissions")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetUserPermissions()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("GetUserPermissions", "GET");
            return res;
        }

        /// <summary>
        /// Search users
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "SearchUsers",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "SearchUsers",
        ///        "QueryParameters": {
        ///            "search_term": "testuser"
        ///         }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("SearchUsers")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> SearchUsers()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("SearchUsers", "GET");
            return res;
        }

        /// <summary>
        /// Add Document Category
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "AddDocumentCategory",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "AddDocumentCategory",
        ///        "PayloadParameters": {
        ///            "key": "Test",
        ///            "value": "Test",
        ///            "color": "#dcc"
        ///         }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("AddDocumentCategory")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> AddDocumentCategory()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("AddDocumentCategory", "POST");
            return res;
        }

        /// <summary>
        /// Delete Document Category
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "DeleteDocumentCategory",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "DeleteDocumentCategory",
        ///        "PathParameters": {
        ///            "category_value": "Test",
        ///         }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DeleteDocumentCategory")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DeleteDocumentCategory()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("DeleteDocumentCategory", "DELETE");
            return res;
        }

        /// <summary>
        /// Create Share
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "CreateShare",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "CreateShare",
        ///        "PayloadParameters": {
        ///            "profile_uuid": "**************-3123",
        ///            "profile_name": "Integral Bank inc.",
        ///            "description": "Profile For Integral Bank inc.",
        ///         }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateShare")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateShare()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("CreateShare", "POST");
            return res;
        }

        /// <summary>
        /// Update Share
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "UpdateShare",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "UpdateShare",
        ///        "PathParameters": {
        ///            "share_id": "1234-1234-1234-1234"
        ///         },
        ///         "PayloadParameters": {
        ///            "users_info": [
        ///              {
        ///                  "username": "user2"
        ///              }
        ///            ]
        ///         }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UpdateShare")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UpdateShare()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("UpdateShare", "PATCH");
            return res;
        }

        /// <summary>
        /// Get Share
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetShare",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "GetShare",
        ///        "PathParameters": {
        ///            "share_id": "1234-1234-1234-1234"
        ///         }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetShare")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetShare()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("GetShare", "GET");
            return res;
        }

        /// <summary>
        /// Get Document Activity feed
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetDocumentActivityFeed",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "GetDocumentActivityFeed",
        ///        "PathParameters": {
        ///            "document_id": "1234-1234-1234-1234"
        ///         },
        ///         "QueryParameters": {
        ///             "Limit": 200,
        ///             "Offset": 0
        ///         }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetDocumentActivityFeed")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetDocumentActivityFeed()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("GetDocumentActivityFeed", "GET");
            return res;
        }

        /// <summary>
        /// Update Document metadatas
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "UpdateDocumentMetadatas",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "UpdateDocumentMetadatas",
        ///        "PathParameters": {
        ///            "document_id": "1234-1234-1234-1234"
        ///         },
        ///         "PayloadParameters": {
        ///            "pxw_profile_id": "4cc0d900-3d9d-472a-8316-93d7eb5da3c7",
        ///            "related_accounts": ["5PXAZZ"],
        ///            "document_category": "Legal",
        ///            "description": "This is amazing"
        ///         }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UpdateDocumentMetadatas")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UpdateDocumentMetadatas()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("UpdateDocumentMetadatas", "POST");
            return res;
        }

        /// <summary>
        /// ListNotifications
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "ListNotifications",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "ListNotifications",
        ///        "PathParameters": {
        ///            "user_id": "<EMAIL>"
        ///         },
        ///         "QueryParameters": {
        ///            "limit": 20,
        ///            "offset": 0,
        ///         }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListNotifications")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListNotifications()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("ListNotifications", "GET");
            return res;
        }

        /// <summary>
        /// ListUnreadNotifications
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "ListUnreadNotifications",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "ListUnreadNotifications",
        ///        "PathParameters": {
        ///            "user_id": "<EMAIL>"
        ///         }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListUnreadNotifications")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListUnreadNotifications()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("ListUnreadNotifications", "GET");
            return res;
        }

        /// <summary>
        /// UserSubscriptionNotifications
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "UserSubscriptionNotifications",
        ///        "TenantGuid": "43e4bbcd-81ea-4806-8919-c8634eae8e62",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "UserSubscriptionNotifications",
        ///        "PathParameters": {
        ///            "user_id": "<EMAIL>"
        ///         },
        ///         "PayloadParameters": {
        ///            "document_id": "1234-1234-1234-1234"
        ///            "events": ["read", "change"]
        ///          }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UserSubscriptionNotifications")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UserSubscriptionNotifications()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("UserSubscriptionNotifications", "POST");
            return res;
        }

        /// <summary>
        /// AddComment
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "AddComment",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "AddComment",
        ///        "PayloadParameters": {
        ///            "user_id": "34e28262-2f67-4ccc-ac46-6f50f5fd1d43",
        ///            "content": "Oh Hello Donkay boi"
        ///        },
        ///        "PathParameters": {
        ///            "document_id": "fcb35125-9fc4-4e8b-93ec-f6d16a37c20d"
        ///        }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("AddComment")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> AddComment()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("AddComment", "POST");
            return res;
        }

        /// <summary>
        /// ListComments
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "ListComments",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "ListComments",
        ///        "PathParameters": {
        ///            "document_id": "fcb35125-9fc4-4e8b-93ec-f6d16a37c20d"
        ///        },
        ///        "QueryParameters": {
        ///            "limit": 50,
        ///            "offset": 0
        ///        }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListComments")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListComments()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("ListComments", "GET");
            return res;
        }

        /// <summary>
        /// GetComment
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetComment",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "GetComment",
        ///        "PathParameters": {
        ///            "document_id": "b980d2b4-0a8d-48f8-acca-ae361902f998",
        ///            "comment_id": "71eb9f7c-a3d9-481f-8737-8fa85d209f6e"
        ///        }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetComment")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetComment()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("GetComment", "GET");
            return res;
        }

        /// <summary>
        /// DeleteComment
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "DeleteComment",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "DeleteComment",
        ///        "PathParameters": {
        ///            "document_id": "b980d2b4-0a8d-48f8-acca-ae361902f998",
        ///            "comment_id": "71eb9f7c-a3d9-481f-8737-8fa85d209f6e"
        ///        }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DeleteComment")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DeleteComment()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("DeleteComment", "DELETE");
            return res;
        }

        /// <summary>
        /// UpdateComment
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "UpdateComment",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "UpdateComment",
        ///        "PathParameters": {
        ///            "document_id": "b980d2b4-0a8d-48f8-acca-ae361902f998",
        ///            "comment_id": "3fb3b55c-f7d6-4022-8553-5bd8754e5ed9"
        ///        },
        ///        "PayloadParameters": {
        ///            "content": "This is the still new content hihi"
        ///        }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UpdateComment")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UpdateComment()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("UpdateComment", "PATCH");
            return res;
        }

        /// <summary>
        /// ListUsers
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "ListUsers",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "ListUsers",
        ///        "PathParameters": {
        ///            "document_id": "b980d2b4-0a8d-48f8-acca-ae361902f998"
        ///        }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListUsers")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListUsers()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("ListUsers", "GET");
            return res;
        }

        /// <summary>
        /// GetCommentUnreadCount
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetCommentUnreadCount",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "GetCommentUnreadCount",
        ///        "PathParameters": {
        ///            "document_id": "38dc8e3d-1831-4be1-92f1-f2b0e9db2262"
        ///        }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetCommentUnreadCount")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetCommentUnreadCount()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("GetCommentUnreadCount", "GET");
            return res;
        }

        /// <summary>
        /// GetAllShares
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "GetAllShares",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "GetAllShares",
        ///        "QueryParameters": {
        ///            "Limit": "200",
        ///            "Offset": "0"
        ///        }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetAllShares")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetAllShares()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("GetAllShares", "GET");
            return res;
        }

        /// <summary>
        /// SetUserEmailNotification
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    POST
        ///    {
        ///        "Component": "SetUserEmailNotification",
        ///        "TenantGuid": "{{TenantGuid}}",
        ///        "Controller": "PXW/DocumentApi",
        ///        "Action": "SetUserEmailNotification",
        ///        "QueryParameters": {
        ///            "opt_in": true
        ///        }
        ///    }
        /// </remarks>
        /// <response code="200">Returns status</response>
        /// <response code="400">Path is invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("SetUserEmailNotification")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> SetUserEmailNotification()
        {
            var res = await _baseService.GetDocumentApiEndpointsWithB2CToken("SetUserEmailNotification", "POST");
            return res;
        }

    }
}
