﻿using MicroSaasWebApi.Common;
using Microsoft.SharePoint.Client;
using OfficeDevPnP.Core;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroSaasWebApi.Functions
{
    public class GetFiles
    {
        public List<string> objColumns { get; set; }
        public List<ListItem> GetAllDcoumentsInaList()
        {
            List<ListItem> obj = new List<ListItem>();
            //  string sitrUrl = "https://SPTenant.sharepoint.com/sites/contosobeta";
            using (var ctx = new AuthenticationManager().GetAppOnlyAuthenticatedContext(General.SiteUrl, General.ClientId, General.ClientSecret))
            {
                //ctx.Credentials = Your Credentials
                ctx.Load(ctx.Web, a => a.Lists);
                ctx.ExecuteQuery();

                List list = ctx.Web.Lists.GetByTitle(General.DocumentLibraryDest);

                Folder folder = ctx.Web.GetFolderByServerRelativeUrl(General.SiteUrl + "/" + General.DocumentLibraryDest + "/" + General.ClientSubFolderDest);
                ctx.Load(folder, f => f.ListItemAllFields);
                ctx.ExecuteQuery();


                //string query = General.CreateCAMLQuery(objColumns, "And", true);
                var fname = "/" + General.DocumentLibraryDest + "/" + General.ClientSubFolderDest;
                string query = General.CreateCAMLQuery(objColumns, "And", true, fname);

                ListItemCollection items = list.GetItems(new CamlQuery() { ViewXml = query });
                try
                {
                    ctx.Load(items);
                    ctx.ExecuteQuery();
                }
                catch (Exception ex)
                {
                    return obj;
                }

                foreach (var doc in items)
                {
                    obj.Add(doc);
                    //Console.WriteLine(doc["FileRef"].ToString().Split('/').LastOrDefault() +
                    //    " (" + doc["File_x0020_Size"].ToString() + " bytes)");
                }
            }
            return obj;
        }

        public static MemoryStream DownloadFilesFromSharePoint(string FileName)
        {
            //Load Libraries from SharePoint
            using (var ctx = new AuthenticationManager().GetAppOnlyAuthenticatedContext(General.SiteUrl, General.ClientId, General.ClientSecret))
            {

                ctx.Load(ctx.Web, a => a.Lists);
                ctx.ExecuteQuery();

                List list = ctx.Web.Lists.GetByTitle(General.DocumentLibrary);

                Microsoft.SharePoint.Client.File file = ctx.Web.GetFileByUrl("/" + General.DocumentLibraryDest + "/" + General.ClientSubFolderDest + "/" + FileName);

                ClientResult<Stream> data = file.OpenBinaryStream();

                // Load the Stream data for the file
                ctx.Load(file);
                ctx.ExecuteQuery();

                MemoryStream memStream = new MemoryStream();
                data.Value.CopyTo(memStream);

                return memStream;

            }

        }

        public List<ListItem> GetFileList(string foldername)
        {
            List<ListItem> obj = new List<ListItem>();
            //  string sitrUrl = "https://SPTenant.sharepoint.com/sites/contosobeta";
            using (var ctx = new AuthenticationManager().GetAppOnlyAuthenticatedContext(General.SiteUrl, General.ClientId, General.ClientSecret))
            {
                //ctx.Credentials = Your Credentials
                ctx.Load(ctx.Web, a => a.Lists);
                ctx.ExecuteQuery();

                List list = ctx.Web.Lists.GetByTitle(General.DocumentLibrary);

                //Folder folder = ctx.Web.GetFolderByServerRelativeUrl(General.SiteUrl + "/" + General.DocumentLibrary + "/" + foldername);
                //ctx.Load(folder, f => f.ListItemAllFields);
                //ctx.ExecuteQuery();



                //var items = list.GetItems(new CamlQuery() { ViewXml = "<View Scope='Recursive'><Query></Query></View>" });

                var fname = "/" + General.DocumentLibrary + "/" + foldername;


                var items = list.GetItems(new CamlQuery() { ViewXml = @"<View Scope='RecursiveAll'><Query><Where><Eq><FieldRef Name='FileDirRef' /><Value Type='Text'>" + fname + "</Value></Eq></Where></Query></View>" });

                ctx.Load(items);
                ctx.ExecuteQuery();


                foreach (var doc in items)
                {
                    obj.Add(doc);
                    //Console.WriteLine(doc["FileRef"].ToString().Split('/').LastOrDefault() +
                    //    " (" + doc["File_x0020_Size"].ToString() + " bytes)");
                }
            }
            return obj;
        }

    }
}
