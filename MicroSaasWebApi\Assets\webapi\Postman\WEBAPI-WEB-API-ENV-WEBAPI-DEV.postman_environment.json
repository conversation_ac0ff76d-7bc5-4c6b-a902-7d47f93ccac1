{"id": "bb0a7500-f36a-4c16-9e18-f1d97d17ccec", "name": "WEBAPI-WEB-API-ENV-WEBAPI-DEV", "values": [{"key": "domain", "value": "https://localhost:7163", "type": "default", "enabled": true}, {"key": "domain.uat", "value": "https://webapi-webapi-dev.azurewebsites.net", "type": "default", "enabled": true}, {"key": "profile_id", "value": "92f5c5d0-1efd-4ce0-bbcb-6e4510852bd8", "type": "default", "enabled": true}, {"key": "account_codes", "value": "[\"5PXRYD\", \"5PXREY\", \"5PXKAD\", \"5PXWAX\", \"5PXKAR\", \"5PXMCA\", \"5PXKAE\"]", "type": "default", "enabled": true}, {"key": "TenantGuid", "value": "3532e645-cf31-428f-832c-0e15bbbb4f82", "type": "default", "enabled": true}, {"key": "account_code", "value": "5PXKAR", "type": "default", "enabled": true}, {"key": "as_of_date", "value": "2022-12-20", "type": "default", "enabled": true}, {"key": "b2c_client_secret", "value": "****************************************", "type": "default", "enabled": true}, {"key": "b2c_client_id", "value": "e3d78e51-11b7-42bf-9b7d-0ff3862ee8d2", "type": "default", "enabled": true}, {"key": "b2c_token_url", "value": "https://devpxwb2c.b2clogin.com/devpxwb2c.onmicrosoft.com/B2C_1A_SIGNIN_TOTP/oauth2/v2.0/token", "type": "default", "enabled": true}, {"key": "b2c_auth_url", "value": "https://devpxwb2c.b2clogin.com/devpxwb2c.onmicrosoft.com/B2C_1A_SIGNIN_TOTP/oauth2/v2.0/authorize", "type": "default", "enabled": true}, {"key": "baseUrl", "value": "**********************************************/webapi/v1.0", "type": "default", "enabled": true}, {"key": "key", "value": "274ad31b5499428f97335e011b4db5bc", "type": "default", "enabled": true}, {"key": "organization", "value": "webapi-01", "type": "default", "enabled": true}, {"key": "userEmail", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "workflowKey", "value": "a3ede38a-855d-4924-8a85-ec6a83177db4", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-08-29T18:22:16.147Z", "_postman_exported_using": "Postman/11.7.0"}