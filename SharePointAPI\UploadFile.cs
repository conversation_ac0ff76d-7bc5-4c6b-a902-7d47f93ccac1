﻿using Microsoft.SharePoint.Client;
//using OfficeDevPnP.Core;
using PnP.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SharePointAPI
{
    public class UploadFile
    {
		
		public string AccountId { get; set; }
		public string Type { get; set; }
		public DateTime? Date { get; set; }
		public string FileId { get; set; }
		public string Mandatory { get; set; }
		public List	<CustomField> customField { get; set; }


        public Tuple<string, string> UploadToLandingZone(byte[] FileContent, string FileName)
        {
            try
            {
                using (var CContext = new AuthenticationManager().GetACSAppOnlyContext(General.SiteUrl, General.ClientId, General.ClientSecret))
                {
                    Web web = CContext.Web;
                    FileCreationInformation newFile = new FileCreationInformation();
                    newFile.ContentStream = new MemoryStream(FileContent);
                    newFile.Url = FileName;
                    List Library = web.Lists.GetByTitle(General.DocumentLibrary);
                    Folder Clientfolder = null;
                    if (General.ClientSubFolder == "")
                    {
                        Clientfolder = Library.RootFolder;
                    }
                    else
                    {
                        Clientfolder = Library.RootFolder.Folders.Add(General.ClientSubFolder);
                        Clientfolder.Update();
                    }
                    Microsoft.SharePoint.Client.File uploadFile = Clientfolder.Files.Add(newFile);

                    CContext.Load(Library);
                    CContext.Load(uploadFile);
                    CContext.ExecuteQuery();

                    //Update Custion Fields
                    if (customField != null && customField.Count() > 0)
                    {
                        foreach (var item in customField)
                        {
                            uploadFile.ListItemAllFields[item.FieldKey] = item.Value;
                        }

                        uploadFile.ListItemAllFields.Update();
                        CContext.ExecuteQuery();

                    }
                    return Tuple.Create("success", "The File has been uploaded" + Environment.NewLine + "FileUrl -->" + General.SiteUrl + "/" + General.DocumentLibrary + "/" + General.ClientSubFolder + "/" + Path.GetFileName(FileName));
                }
            }
            catch (Exception ex)
            {
                return Tuple.Create("failed", ex.Message);
                throw ex;
            }
        }
        public  Tuple<string, string> Upload(byte[] FileContent, string FileName)
        {		
			try
			{
				using (var CContext = new AuthenticationManager().GetACSAppOnlyContext(General.SiteUrl, General.ClientId, General.ClientSecret))
				{	
					Web web = CContext.Web;
					FileCreationInformation newFile = new FileCreationInformation();
					newFile.ContentStream = new MemoryStream(FileContent);
					newFile.Url = FileName;
					List Library = web.Lists.GetByTitle(General.DocumentLibraryDest);
					Folder Clientfolder = null;
					if (General.ClientSubFolderDest == "")
					{
						Clientfolder = Library.RootFolder;
					}
					else
					{
						Clientfolder = Library.RootFolder.Folders.Add(General.ClientSubFolderDest);
						Clientfolder.Update();
					}
					Microsoft.SharePoint.Client.File uploadFile = Clientfolder.Files.Add(newFile);
					
					CContext.Load(Library);
					CContext.Load(uploadFile);					
					CContext.ExecuteQuery();

					//Update Custion Fields
					if (customField != null && customField.Count() > 0)
                    {
                        foreach (var item in customField)
                        {
							uploadFile.ListItemAllFields[item.FieldKey] = item.Value;
						}

						uploadFile.ListItemAllFields.Update();
						CContext.ExecuteQuery();

					}
					return Tuple.Create("success", "The File has been uploaded" + Environment.NewLine + "FileUrl -->" + General.SiteUrl + "/" + General.DocumentLibrary + "/" + General.ClientSubFolder + "/" + Path.GetFileName(FileName));
				}
			} catch(Exception ex)
            {
				return Tuple.Create("failed", ex.Message);
				throw ex;
            }
		}

		public Tuple<string, string> UploadFileByPath(Guid fileid, string NewFileName, string fileExt)
		{
			try
			{
				using (var ctx = new AuthenticationManager().GetACSAppOnlyContext(General.SiteUrl, General.ClientId, General.ClientSecret))
				{

					ctx.Load(ctx.Web, a => a.Lists);
					ctx.ExecuteQuery();
					List list = ctx.Web.Lists.GetByTitle(General.DocumentLibrary);

					Microsoft.SharePoint.Client.File file = ctx.Web.GetFileById(fileid);
					//var targetFileUrl = file.MoveTo("/" + General.DocumentLibrary + "/" + General.des + "/" + FileName);
					file.MoveTo("/"+ General.DocumentLibraryDest + "/"+ General.ClientSubFolderDest + "/" + NewFileName + "."+ fileExt, MoveOperations.None);
					ctx.ExecuteQuery();

                    //Update Custion Fields
                    if (customField != null && customField.Count() > 0)
                    {
                        foreach (var item in customField)
                        {
							file.ListItemAllFields[item.FieldKey] = item.Value;
                        }

						file.ListItemAllFields.Update();
                        ctx.ExecuteQuery();

                    }

                    return Tuple.Create("success", " file moved");
				}
			} catch(Exception ex){
				return Tuple.Create("failed", ex.Message);
				throw ex;
			}
			
			
		}

	
	}
	
	public class CustomField
    {
		public string FieldKey { get; set; }
		public object Value { get; set; }
		
	}
}
