﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using Swashbuckle.AspNetCore.Filters;

namespace MicroSaasWebApi.Controllers.PXW
{
    [Route("api/PXW/[controller]")]
    [ApiController]
    [EnableCors("AllowAll")]
    public class PermissionApiController : BaseController
    {
        public PermissionApiController(IBaseService baseService, ILoggerService loggerService) : base(baseService, loggerService)
        {
        }

        /// <summary>
        /// Retrieves the list of users
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "ListUsers",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/PermissionApi",
        ///         "Action": "ListUsers"
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the list of users</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListUsers")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListUsers()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("ListUsers", true, "GET");
        }

        /// <summary>
        /// Retrieves a user
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "GetUser",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "GetUser",
        ///       "PathParameters": {
        ///           "user_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns a user</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetUser()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("GetUser", true, "GET");
        }

        /// <summary>
        /// Update a user
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "UpdateUser",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "UpdateUser",
        ///       "PathParameters": {
        ///           "user_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///       },
        ///       "PayloadParameters": {
        ///          "first_name": "Francisco",
        ///          "last_name": "DeLaRiviera",
        ///          "fileshare_username": "<EMAIL>"
        ///        }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns a user</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UpdateUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UpdateUser()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("UpdateUser", true, "PATCH");
        }

        /// <summary>
        /// Create a user
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "CreateUser",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "CreateUser",
        ///       "PayloadParameters": {
        ///           "firstname": "Francis",
        ///           "lastname": "Test",
        ///           "email": "<EMAIL>"
        ///        }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns a user</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateUser()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("CreateUser", true, "POST");
        }

        /// <summary>
        /// set login datetime on user
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "SetUserLoginDatetime",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "SetUserLoginDatetime"
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns a user</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("SetUserLoginDatetime")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> SetUserLoginDatetime()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("SetUserLoginDatetime", true, "GET");
        }

        /// <summary>
        /// search profiles-accounts
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "ProfilesAccountsSearch",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "ProfilesAccountsSearch",
        ///       "PayloadParameters": {
        ///           "searchTerm": "test"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the list of accounts matching search terms by account code or profile name</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ProfilesAccountsSearch")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesAccountsSearch()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("ProfilesAccountsSearch", true, "POST");
        }

        /// <summary>
        /// delete a user
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "DeleteUser",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "DeleteUser",
        ///       "PathParameters": {
        ///           "user_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns a user</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DeleteUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DeleteUser()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("DeleteUser", true, "DELETE");
        }

        /// <summary>
        /// Send user invite
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "SendInviteUser",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "SendInviteUser",
        ///       "PathParameters": {
        ///           "user_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns a user</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("SendInviteUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> SendInviteUser()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("SendInviteUser", true, "GET");
        }

        /// <summary>
        /// Get current user info
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "CurrentUser",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "CurrentUser"
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns a user</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CurrentUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CurrentUser()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("CurrentUser", true, "GET");
        }

        /// <summary>
        /// Retrieves the list of user profiles
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "ListUserProfiles",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/PermissionApi",
        ///         "Action": "ListUserProfiles",
        ///         "PathParameters": {
        ///             "user_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the list of users</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListUserProfiles")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListUserProfiles()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("ListUserProfiles", true, "GET");
        }

        /// <summary>
        /// Assign or unassign profile to user
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "AssignUserProfile",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/PermissionApi",
        ///         "Action": "AssignUserProfile",
        ///         "PathParameters": {
        ///             "user_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286",
        ///             "profile_id": "e6c4441f-e869-40ef-838c-3e13a96d73e6"
        ///         },
        ///         "PayloadParameters": {
        ///             "is_assigned": false
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the list of users</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("AssignUserProfile")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> AssignUserProfile()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("AssignUserProfile", true, "PUT");
        }

        /// <summary>
        /// Retrieves the list of user roles
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "ListUserRoles",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/PermissionApi",
        ///         "Action": "ListUserRoles",
        ///         "PathParameters": {
        ///             "user_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the list of users</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListUserRoles")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListUserRoles()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("ListUserRoles", true, "GET");
        }

        /// <summary>
        /// Assign role to user
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///         "Component": "AssignUserRole",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/PermissionApi",
        ///         "Action": "AssignUserRole",
        ///         "PathParameters": {
        ///             "user_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286",
        ///             "role_id": "e6c4441f-e869-40ef-838c-3e13a96d73e6"
        ///         }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the user role</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("AssignUserRole")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> AssignUserRole()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("AssignUserRole", true, "PUT");
        }

        /// <summary>
        /// List Profiles
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "ListProfiles",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "ListProfiles"
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the list of profiles</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListProfiles")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListProfiles()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("ListProfiles", true, "GET");
        }

        /// <summary>
        /// Retrieves a profile
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "GetProfile",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "GetProfile",
        ///       "PathParameters": {
        ///           "profile_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns a profile</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetProfile")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetProfile()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("GetProfile", true, "GET");
        }

        /// <summary>
        /// create a profile
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "CreateProfile",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "CreateProfile",
        ///       "PayloadParameters": {
        ///           "name": "testing profile"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the created profile</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateProfile")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateProfile()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("CreateProfile", true, "POST");
        }

        /// <summary>
        /// update a profile
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "UpdateProfile",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "UpdateProfile",
        ///       "PathParameters": {
        ///           "profile_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///       },
        ///       "PayloadParameters": {
        ///           "name": "tatata",
        ///           "key": "a key",
        ///           "completed": true,
        ///           "bypass": true
        ///        }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the updated profile</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UpdateProfile")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UpdateProfile()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("UpdateProfile", true, "PATCH");
        }

        /// <summary>
        /// delete a profile
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "DeleteProfile",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "DeleteProfile",
        ///       "PathParameters": {
        ///           "profile_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Return success message</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DeleteProfile")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DeleteProfile()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("DeleteProfile", true, "DELETE");
        }

        /// <summary>
        /// List profile accounts
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "ListProfileAccounts",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "ListProfileAccounts",
        ///       "PathParameters": {
        ///           "profile_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Return account associated to profile</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListProfileAccounts")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListProfileAccounts()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("ListProfileAccounts", true, "GET");
        }

        /// <summary>
        /// List profile accounts
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "AssignProfileAccount",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "AssignProfileAccount",
        ///       "PathParameters": {
        ///           "profile_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286",
        ///           "account_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///       },
        ///       "PayloadParameters": {
        ///           "is_assigned": true
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Return success message</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("AssignProfileAccount")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> AssignProfileAccount()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("AssignProfileAccount", true, "PUT");
        }

        /// <summary>
        /// List Accounts
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "ListAccounts",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "ListAccounts",
        ///        "QueryParameters": {
        ///         "with_profiles": true,
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the list of profiles</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListAccounts")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListAccounts()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("ListAccounts", true, "GET");
        }

        /// <summary>
        /// Get Account
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "GetAccount",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "GetAccount",
        ///       "PathParameters": {
        ///           "account_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///        }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the list of profiles</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetAccount")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetAccount()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("GetAccount", true, "GET");
        }

        /// <summary>
        /// Create Account
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "CreateAccount",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "CreateAccount",
        ///       "PayloadParameters": {
        ///           "profile_id": "54510ea8-8640-469c-9849-3c8627ed5951",
        ///           "name": "waow",
        ///           "friendlyName": "Owen Wilson",
        ///           "type": "Cash",
        ///           "accountCode": "ZAZVZV",
        ///           "accountCurrency": "CAD"
        ///        }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns created account</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateAccount")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateAccount()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("CreateAccount", true, "POST");
        }

        /// <summary>
        /// Delete Account
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "DeleteAccount",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "DeleteAccount",
        ///       "PathParameters": {
        ///           "account_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///        }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns a success message</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DeleteAccount")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DeleteAccount()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("DeleteAccount", true, "DELETE");
        }

        /// <summary>
        /// Update Account
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "UpdateAccount",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "UpdateAccount",
        ///       "PathParameters": {
        ///           "account_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///        },
        ///        "PayloadParameters": {
        ///            "name": "Nice Account Name",
        ///            "friendlyName": "Fast Food Funds Account",
        ///        }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the updated account</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UpdateAccount")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UpdateAccount()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("UpdateAccount", true, "PATCH");
        }

        /// <summary>
        /// List Roles
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "ListRoles",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "ListRoles"
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the list of roles</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListRoles")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListRoles()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("ListRoles", true, "GET");
        }

        /// <summary>
        /// Get Role
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "GetRole",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "GetRole",
        ///       "PathParameters": {
        ///           "role_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the requested role</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetRole")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetRole()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("GetRole", true, "GET");
        }

        /// <summary>
        /// Create Role
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "CreateRole",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "CreateRole",
        ///           "PayloadParameters": {
        ///               "name": "testroleoulala",
        ///               "description": "this is a test",
        ///               "permissions": [
        ///                   "fake-permission"
        ///               ]
        ///           }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the created role</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateRole")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateRole()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("CreateRole", true, "POST");
        }

        /// <summary>
        /// Update Role
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "UpdateRole",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "UpdateRole",
        ///       "PathParameters": {
        ///           "role_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///       },
        ///       "PayloadParameters": {
        ///           "name": "testrolehihi",
        ///           "description": "this is a test with updated description",
        ///           "permissions": [
        ///               "fake-permission",
        ///               "fake-permission2"
        ///           ]
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the updated role</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UpdateRole")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UpdateRole()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("UpdateRole", true, "PUT");
        }

        /// <summary>
        /// Delete Role
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "DeleteRole",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "DeleteRole",
        ///       "PathParameters": {
        ///           "role_id": "4a2c25e0-5dde-4d68-918e-cc8d68e5b286"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns a success message</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DeleteRole")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DeleteRole()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("DeleteRole", true, "DELETE");
        }

        /// <summary>
        /// List Profile Groups
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "ListProfileGroups",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "ListProfileGroups",
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the created role</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListProfileGroups")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListProfileGroups()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("ListProfileGroups", true, "GET");
        }

        /// <summary>
        /// Create Profile Group
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "CreateProfileGroup",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "CreateProfileGroup",
        ///       "PayloadParameters": {
        ///         "name": "Test Profile",
        ///         "profileIds": [
        ///             "760DAE76-EED6-4683-B43D-946EDCF6579E"
        ///         ]
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the created role</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateProfileGroup")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreateProfileGroup()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("CreateProfileGroup", true, "POST");
        }

        /// <summary>
        /// Get Profile Group
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "GetProfileGroup",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "GetProfileGroup",
        ///       "PathParameters": {
        ///         "profile_group_id": "760DAE76-EED6-4683-B43D-946EDCF6579E"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the created role</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("GetProfileGroup")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetProfileGroup()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("GetProfileGroup", true, "GET");
        }

        /// <summary>
        /// Partially Update Profile Group
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "PartiallyUpdateProfileGroup",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "PartiallyUpdateProfileGroup",
        ///       "PathParameters": {
        ///         "profile_group_id": "760DAE76-EED6-4683-B43D-946EDCF6579E"
        ///       },
        ///       "PayloadParameters": {
        ///         "name": "Updated Name"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the created role</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("PartiallyUpdateProfileGroup")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> PartiallyUpdateProfileGroup()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("PartiallyUpdateProfileGroup", true, "PATCH");
        }

        /// <summary>
        /// Delete Profile Group
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "DeleteProfileGroup",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "DeleteProfileGroup",
        ///       "PathParameters": {
        ///         "profile_group_id": "760DAE76-EED6-4683-B43D-946EDCF6579E"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the created role</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DeleteProfileGroup")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DeleteProfileGroup()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("DeleteProfileGroup", true, "DELETE");
        }

        /// <summary>
        /// Assign Profiles To Profile Group
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "AssignProfilesToProfileGroup",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "AssignProfilesToProfileGroup",
        ///       "PathParameters": {
        ///         "profile_group_id": "760DAE76-EED6-4683-B43D-946EDCF6579E"
        ///       },
        ///       "PayloadParameters": {
        ///         "profileIds": [
        ///           "C5BB7FD1-E761-4F9A-8C20-F84A8A13056E"
        ///         ]
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the created role</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("AssignProfilesToProfileGroup")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> AssignProfilesToProfileGroup()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("AssignProfilesToProfileGroup", true, "POST");
        }

        /// <summary>
        /// Unassign Profiles To Profile Group
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "UnassignProfilesToProfileGroup",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "UnassignProfilesToProfileGroup",
        ///       "PathParameters": {
        ///         "profile_group_id": "760DAE76-EED6-4683-B43D-946EDCF6579E"
        ///       },
        ///       "PayloadParameters": {
        ///         "profileIds": [
        ///           "C5BB7FD1-E761-4F9A-8C20-F84A8A13056E"
        ///         ]
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the created role</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UnassignProfilesToProfileGroup")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UnassignProfilesToProfileGroup()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("UnassignProfilesToProfileGroup", true, "POST");
        }

        /// <summary>
        /// List User Profile Groups
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "ListUserProfileGroups",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "ListUserProfileGroups",
        ///       "PathParameters": {
        ///         "user_id": "4215DC2B-0FC7-4C43-8DF0-F3BAAACF1B37"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the created role</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListUserProfileGroups")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListUserProfileGroups()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("ListUserProfileGroups", true, "GET");
        }

        /// <summary>
        /// Assign Profile Group To User
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "AssignProfileGroupToUser",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "AssignProfileGroupToUser",
        ///       "PathParameters": {
        ///         "user_id": "4215DC2B-0FC7-4C43-8DF0-F3BAAACF1B37",
        ///         "profile_group_id": "760DAE76-EED6-4683-B43D-946EDCF6579E"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the created role</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("AssignProfileGroupToUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> AssignProfileGroupToUser()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("AssignProfileGroupToUser", true, "PUT");
        }

        /// <summary>
        /// Unassign Profile Group To User
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "UnassignProfileGroupToUser",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "UnassignProfileGroupToUser",
        ///       "PathParameters": {
        ///         "user_id": "4215DC2B-0FC7-4C43-8DF0-F3BAAACF1B37",
        ///         "profile_group_id": "760DAE76-EED6-4683-B43D-946EDCF6579E"
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the created role</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UnassignProfileGroupToUser")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UnassignProfileGroupToUser()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("UnassignProfileGroupToUser", true, "PUT");
        }

        /// <summary>
        /// batch assign/unassign users to profile
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "BatchAssignUsersToProfile",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "BatchAssignUsersToProfile",
        ///       "PathParameters": {
        ///         "profile_id": "760DAE76-EED6-4683-B43D-946EDCF6579E"
        ///       },
        ///       "PayloadParameters": {
        ///         "is_assigned: true,
        ///         "user_ids": []
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the updated profile</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("BatchAssignUsersToProfile")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> BatchAssignUsersToProfile()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("BatchAssignUsersToProfile", true, "POST");
        }

        /// <summary>
        /// batch assign/unassign users from profile group
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST
        ///     {
        ///       "Component": "BatchAssignUsersToProfileGroup",
        ///       "TenantGuid": "{{TenantGuid}}",
        ///       "Controller": "PXW/PermissionApi",
        ///       "Action": "BatchAssignUsersToProfileGroup",
        ///       "PathParameters": {
        ///         "profile_group_id": "760DAE76-EED6-4683-B43D-946EDCF6579E"
        ///       },
        ///       "PayloadParameters": {
        ///         "is_assigned: true,
        ///         "user_ids": []
        ///       }
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Returns the updated profile group</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("BatchAssignUsersToProfileGroup")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> BatchAssignUsersToProfileGroup()
        {
            return await _baseService.FetchApiEndpointsPermissionWithOptionalToken("BatchAssignUsersToProfileGroup", true, "POST");
        }

    }
}
