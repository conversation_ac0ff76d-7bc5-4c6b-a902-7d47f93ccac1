using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using Swashbuckle.AspNetCore.Annotations;
using Swashbuckle.AspNetCore.Filters;

namespace MicroSaasWebApi.Controllers.PXW
{
    [Route("api/PXW/[controller]")]
    [ApiController]
    [EnableCors("AllowAll")]
    public class DataApiController : BaseController
    {
        public DataApiController(IBaseService baseService, ILoggerService loggerService) : base(baseService, loggerService)
        {
        }

        #region Ledger

        #region Private Securities
        /// <summary>
        /// Creates a private security.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "CreatePrivateSecurity",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "CreatePrivateSecurity",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}"
        ///         },
        ///         "QueryParameters": {
        ///             "security_code": "345THATDRIVE",
        ///             "security_symbol": "",
        ///             "security_name": "345 That Drive",
        ///             "security_description": "345 That Drive",
        ///             "security_name_description": "345 That Drive",
        ///             "security_type_code": "RLES",
        ///             "security_country_of_issue": "CA",
        ///             "security_country_of_exposure": "CA",
        ///             "security_currency_code": "CAD",
        ///             "asset_class_level1": "Real Assets",
        ///             "asset_class_level2": "Real Estate",
        ///             "related_security_id": null,
        ///             "account_code": "FKPERSONALPVT",
        ///             "transaction_type_code": "SRD",
        ///             "trade_date": "2023-08-31",
        ///             "settle_date": "2023-08-31",
        ///             "quantity": "0.********",
        ///             "unit_price": "1300000.********",
        ///             "settlement_currency_code": "CAD"
        ///         }
        ///     }
        /// </remarks>
        /// <response code="200">Returns the successful response once a private security is created.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreatePrivateSecurity")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreatePrivateSecurity()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("CreatePrivateSecurity", true, "POST");
        }


        /// <summary>
        /// Partially Update Private Security by ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "PartiallyUpdatePrivateSecurityByID",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "PartiallyUpdatePrivateSecurityByID",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}",
        ///             "id" : "{{security_id}}"
        ///         },
        ///         "QueryParameters": {
        ///             "security_description": "Der Schrei der Natur (1893) by Edvard Munch",
        ///             "security_name_description": "Der Schrei der Natur (1893) by Edvard Munch"
        ///         }
        ///     }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("PartiallyUpdatePrivateSecurityByID")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> PartiallyUpdatePrivateSecurityByID()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("PartiallyUpdatePrivateSecurityByID", true, "PATCH");
        }

        /// <summary>
        /// Retrieve a Private Security by ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "RetrievePrivateSecurityByID",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "RetrievePrivateSecurityByID",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}",
        ///             "id" : "{{security_id}}"
        ///         },
        ///         "QueryParameters": {}
        ///     }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("RetrievePrivateSecurityByID")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> RetrievePrivateSecurityByID()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("RetrievePrivateSecurityByID", true);
        }

        /// <summary>
        /// List Private Securities by Profile ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "ListPrivateSecuritiesByProfileID",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "ListPrivateSecuritiesByProfileID",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}"
        ///         },
        ///         "QueryParameters": {}
        ///     }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("ListPrivateSecuritiesByProfileID")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListPrivateSecuritiesByProfileID()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ListPrivateSecuritiesByProfileID", true);
        }

        #endregion

        #region Private Transactions
        /// <summary>
        /// Creates a Private Transaction.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "CreatePrivateTransaction",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "CreatePrivateTransaction",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}",
        ///             "security_pk" : "{{security_id}}"
        ///         },
        ///         "QueryParameters": {
        ///             "account_code": "FKPERSONALPVT",
        ///             "transaction_type_code": "JBY",
        ///             "trade_date": "2023-11-17",
        ///             "settle_date": "2023-11-20",
        ///             "quantity": "1.000000",
        ///             "unit_price": "1600000.000000",
        ///             "settlement_currency_code": "CAD"
        ///         }
        ///     }
        /// </remarks>

        /// <response code="200">Returns the successful response once a private security is created.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreatePrivateTransaction")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreatePrivateTransaction()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("CreatePrivateTransaction", true, "POST");
        }

        /// <summary>
        /// Partially Update Private Transaction by ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "PartiallyUpdatePrivateTransactionByID",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "PartiallyUpdatePrivateTransactionByID",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}",
        ///             "security_pk" : "{{security_id}}",
        ///             "id" : "{{transaction_id}}"
        ///         },
        ///         "QueryParameters": {
        ///             "unit_price": "1599999.990000"
        ///         }
        ///         
        ///     }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("PartiallyUpdatePrivateTransactionByID")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> PartiallyUpdatePrivateTransactionByID()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("PartiallyUpdatePrivateTransactionByID", true, "PATCH");
        }

        /// <summary>
        /// Retrieve a Private Transaction by ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "RetrievePrivateTransactionByID",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "RetrievePrivateTransactionByID",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}",
        ///             "security_pk" : "{{security_id}}",
        ///             "id" : "{{transaction_id}}"
        ///         },
        ///         "QueryParameters": {}
        ///     }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("RetrievePrivateTransactionByID")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> RetrievePrivateTransactionByID()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("RetrievePrivateTransactionByID", true);
        }

        /// <summary>
        /// List Private Transactions by Security ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "ListPrivateTransactionsBySecurityID",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "ListPrivateTransactionsBySecurityID",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}",
        ///             "security_pk" : "{{security_id}}"
        ///         },
        ///         "QueryParameters": {}
        ///     }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("ListPrivateTransactionsBySecurityID")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListPrivateTransactionsBySecurityID()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ListPrivateTransactionsBySecurityID", true);
        }

        /// <summary>
        /// Destroy Private Transaction by ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "DestroyPrivateTransactionByID",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "DestroyPrivateTransactionByID",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}",
        ///             "security_pk" : "{{security_id}}",
        ///             "id" : "{{transaction_id}}"
        ///         },
        ///         "QueryParameters": {
        ///         }
        ///         
        ///     }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("DestroyPrivateTransactionByID")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DestroyPrivateTransactionByID()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("DestroyPrivateTransactionByID", true, "DELETE");
        }
        #endregion

        #region Private Security Prices

        /// <summary>
        /// Creates a Private SecurityPrice.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "CreatePrivateSecurityPrice",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "CreatePrivateSecurityPrice",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}",
        ///             "security_pk" : "{{security_id}}"
        ///         },
        ///         "QueryParameters": {
        ///             "price": "2000000.000000",
        ///             "price_as_of": "2023-11-23"
        ///         }
        ///     }
        /// </remarks>

        /// <response code="200">Returns the successful response once a private security is created.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreatePrivateSecurityPrice")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CreatePrivateSecurityPrice()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("CreatePrivateSecurityPrice", true, "POST");
        }

        /// <summary>
        /// Retrieve a Private SecurityPrice by ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "RetrievePrivateSecurityPriceByID",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "RetrievePrivateSecurityPriceByID",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}",
        ///             "security_pk" : "{{security_id}}"
        ///         },
        ///         "QueryParameters": {}
        ///     }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("RetrievePrivateSecurityPriceByID")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> RetrievePrivateSecurityPriceByID()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("RetrievePrivateSecurityPriceByID", true);
        }

        /// <summary>
        /// List Private SecurityPrices by Security ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "ListPrivateSecurityPricesBySecurityID",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "ListPrivateSecurityPricesBySecurityID",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}",
        ///             "security_pk" : "{{security_id}}"
        ///         },
        ///         "QueryParameters": {}
        ///     }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("ListPrivateSecurityPricesBySecurityID")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ListPrivateSecurityPricesBySecurityID()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ListPrivateSecurityPricesBySecurityID", true);
        }

        /// <summary>
        /// Partially Update Private SecurityPrice by ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "PartiallyUpdatePrivateSecurityPriceByID",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "PartiallyUpdatePrivateSecurityPriceByID",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}",
        ///             "security_pk" : "{{security_id}}",
        ///             "id" : "{{transaction_id}}"
        ///         },
        ///         "QueryParameters": {
        ///             "unit_price": "1599999.990000"
        ///         }
        ///         
        ///     }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("PartiallyUpdatePrivateSecurityPriceByID")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> PartiallyUpdatePrivateSecurityPriceByID()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("PartiallyUpdatePrivateSecurityPriceByID", true, "PATCH");
        }

        /// <summary>
        /// Destroy Private SecurityPrice by ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "DestroyPrivateSecurityPriceByID",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "DestroyPrivateSecurityPriceByID",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}",
        ///             "security_pk" : "{{security_id}}",
        ///             "id" : "{{transaction_id}}"
        ///         },
        ///         "QueryParameters": {
        ///         }
        ///         
        ///     }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("DestroyPrivateSecurityPriceByID")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DestroyPrivateSecurityPriceByID()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("DestroyPrivateSecurityPriceByID", true, "DELETE");
        }
        #endregion

        /// <summary>
        /// Get AssetClasses Data.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "RetrieveAssetClass",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "RetrieveAssetClass",
        ///         "PathParameters": {
        ///         },
        ///         "QueryParameters": {
        ///         }
        ///     }
        /// </remarks>

        /// <response code="200">Returns the successful response once a private security is created.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("RetrieveAssetClass")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> RetrieveAssetClass()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("RetrieveAssetClass", true, "GET");
        }

        /// <summary>
        /// Get Security Code.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "RetrieveSecurityCode",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "RetrieveSecurityCode",
        ///         "PathParameters": {
        ///         },
        ///         "QueryParameters": {
        ///             "security_name": "Mona Lisa (1503)"
        ///         }
        ///     }
        /// </remarks>

        /// <response code="200">Returns the successful response once a private security is created.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("RetrieveSecurityCode")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> RetrieveSecurityCode()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("RetrieveSecurityCode", true, "GET");
        }

        /// <summary>
        /// Get the Security Types.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "RetrieveSecurityTypes",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "RetrieveSecurityTypes",
        ///         "PathParameters": {
        ///         },
        ///         "QueryParameters": {
        ///         }
        ///     }
        /// </remarks>

        /// <response code="200">Returns the successful response once a private security is created.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("RetrieveSecurityTypes")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> RetrieveSecurityTypes()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("RetrieveSecurityTypes", true, "GET");
        }

        /// <summary>
        /// Retrieve DataPipeline Status.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "RetrieveDataPipelineStatus",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "RetrieveDataPipelineStatus",
        ///         "PathParameters": {
        ///         },
        ///         "QueryParameters": {
        ///         }
        ///     }
        /// </remarks>        
        /// <response code="200">Returns the successful response once a private security is created.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("RetrieveDataPipelineStatus")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> RetrieveDataPipelineStatus()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("RetrieveDataPipelineStatus", true, "GET");
        }

        /// <summary>
        /// Export Private Securities Template by Profile ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api
        ///     {
        ///         "Component": "ExportPrivateSecuritiesTemplate",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "ExportPrivateSecuritiesTemplate",
        ///         "PathParameters": {
        ///             "profile_pk" : "{{profile_id}}",
        ///         },
        ///         "QueryParameters": {
        ///         }
        ///     }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("ExportPrivateSecuritiesTemplate")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ExportPrivateSecuritiesTemplate()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ExportPrivateSecuritiesTemplate", true, "GET");
        }

        /// <summary>
        /// Upload Private Securities Template by Profile ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        /// This request is uses a FormData request body.
        /// Here are the keys of the form data:
        ///
        ///     POST /api
        ///     {
        ///       documents: list of files to upload
        ///       path: path to upload to
        ///       TenantGuid: tenant guid
        ///       Controller: PXW/DataApi
        ///       Action: UploadPrivateSecuritiesTemplate
        ///       Component: UploadPrivateSecuritiesTemplate
        ///     }
        ///
        /// </remarks>
        /// <returns></returns>
        [HttpPost("UploadPrivateSecuritiesTemplate")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> UploadPrivateSecuritiesTemplate()
        {
            await _loggerService.LogInformationAsync("Upload Private Securities Template");
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("UploadPrivateSecuritiesTemplate", true, "POST");
        }
        #endregion

        #region Common
        /// <summary>
        /// Retrieves the list of currencies
        /// </summary>
        ///     POST /api
        ///     {
        ///     "Component": "CurrenciesRetrieveDropdown",
        ///     "TenantGuid": "{{TenantGuid}}",
        ///     "Controller": "PXW/DataApi",
        ///     "Action": "CurrenciesList"
        ///     }
        /// 
        /// <response code="200">Returns the account summary data</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CurrenciesList")]
        [SwaggerOperation("CurrenciesList")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> CurrenciesList()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("CurrenciesList", false);
        }

        /// <summary>
        /// Retrieves the summary of profile accounts
        /// </summary>
        /// <remarks>
        /// Retrieves the summary of profile accounts, considering optional parameters like as_of_date, currency, and registration status.
        /// </remarks>

        /// <response code="200">Returns the account summary data</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ProfilesAccountsSummaryRetrieve")]
        [SwaggerOperation("ProfilesAccountsSummaryRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesAccountsSummaryRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesAccountsSummaryRetrieve", true);
        }

        /// <summary>
        /// Retrieves the list of AsofDates for a profile.
        /// </summary>        
        /// <response code="200">Returns the account summary data</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ProfilesAsOfDatesRetrieve")]
        [SwaggerOperation("ProfilesAsOfDatesRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesAsOfDatesRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesAsOfDatesRetrieve", true);
        }

        [HttpPost("ProfilesCustodianAccountCodesRetrieve")]
        [SwaggerOperation("ProfilesCustodianAccountCodesRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesCustodianAccountCodesRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesCustodianAccountCodesRetrieve", true);
        }

        [HttpPost("ProfilesDashboardLineGraphRetrieve")]
        [SwaggerOperation("ProfilesDashboardLineGraphRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesDashboardLineGraphRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesDashboardLineGraphRetrieve", true);
        }


        [HttpPost("ProfilesDashboardSummaryRetrieve")]
        [SwaggerOperation("ProfilesDashboardSummaryRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesDashboardSummaryRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesDashboardSummaryRetrieve", true);
        }

        [HttpPost("ProfilesPortfolioSummaryRetrieve")]
        [SwaggerOperation("ProfilesPortfolioSummaryRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesPortfolioSummaryRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesPortfolioSummaryRetrieve", true);
        }

        [HttpPost("ProfilesFundingsRetrieve")]
        [SwaggerOperation("ProfilesFundingsRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesFundingsRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesFundingsRetrieve", true);
        }

        [HttpPost("ProfilesHoldingsRetrieve")]
        [SwaggerOperation("ProfilesHoldingsRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesHoldingsRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesHoldingsRetrieve", true);
        }

        /// <summary>
        /// Retreives a All Holdings Data for a Profile ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api
        ///     {
        ///         "Component": "ProfilesAllHoldingsRetrieve",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "ProfilesAllHoldingsRetrieve",
        ///         "PathParameters": {
        ///             "id" : "{{profile_id}}"
        ///         },
        ///         "QueryParameters": {
        ///         }
        ///     }
        /// </remarks>        
        /// <response code="200">Returns the successful response of All Holdings Data for a Profile ID.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ProfilesAllHoldingsRetrieve")]
        [SwaggerOperation("ProfilesAllHoldingsRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesAllHoldingsRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesAllHoldingsRetrieve", true);
        }

        [HttpPost("ProfilesPieChartByAccountRetrieve")]
        [SwaggerOperation("ProfilesPieChartByAccountRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesPieChartByAccountRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesPieChartByAccountRetrieve", true);
        }

        [HttpPost("ProfilesPieChartByAssetClassRetrieve")]
        [SwaggerOperation("ProfilesPieChartByAssetClassRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesPieChartByAssetClassRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesPieChartByAssetClassRetrieve", true);
        }

        [HttpPost("ProfilesPieChartByCurrencyRetrieve")]
        [SwaggerOperation("ProfilesPieChartByCurrencyRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesPieChartByCurrencyRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesPieChartByCurrencyRetrieve", true);
        }

        [HttpPost("ProfilesPieChartsRetrieve")]
        [SwaggerOperation("ProfilesPieChartsRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesPieChartsRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesPieChartsRetrieve", true);
        }

        [HttpPost("ProfilesTopHoldingsRetrieve")]
        [SwaggerOperation("ProfilesTopHoldingsRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesTopHoldingsRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesTopHoldingsRetrieve", true);
        }

        [HttpPost("ProfilesTransactionsRetrieve")]
        [SwaggerOperation("ProfilesTransactionsRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesTransactionsRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesTransactionsRetrieve", true);
        }

        [HttpPost("ProfilesAccountsList")]
        [SwaggerOperation("ProfilesAccountsList")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesAccountsList()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesAccountsList", true);
        }

        [HttpPost("ProfilesAccountsHoldingsRetrieve")]
        [SwaggerOperation("ProfilesAccountsHoldingsRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesAccountsHoldingsRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesAccountsHoldingsRetrieve", true);
        }

        [HttpPost("ProfilesAccountsTransactionsRetrieve")]
        [SwaggerOperation("ProfilesAccountsTransactionsRetrieve")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> ProfilesAccountsTransactionsRetrieve()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ProfilesAccountsTransactionsRetrieve", true);
        }

        /// <summary>
        /// Logs the login event of a user
        /// </summary>
        /// <remarks>
        /// Logs the login event of a usery
        /// </remarks>

        /// <response code="200">Returns the logged activity</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("LogUserLogin")]
        [SwaggerOperation("LogUserLogin")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> LogUserLogin()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("LogUserLogin", true, "POST");
        }

        /// <summary>
        /// Logs the logout event of a user
        /// </summary>
        /// <remarks>
        /// Logs the logout event of a usery
        /// </remarks>

        /// <response code="200">Returns the logged activity</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("LogUserLogout")]
        [SwaggerOperation("LogUserLogout")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> LogUserLogout()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("LogUserLogout", true, "POST");
        }

        /// <response code="200">Returns the logged activity</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("PresetDateRanges")]
        [SwaggerOperation("PresetDateRanges")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> PresetDateRanges()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("PresetDateRanges", true);
        }

        /// <response code="200">Returns the logged activity</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("AccountLevelPerformance")]
        [SwaggerOperation("AccountLevelPerformance")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> AccountLevelPerformance()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("AccountLevelPerformance", true);
        }

        /// <response code="200">Returns the logged activity</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("RorTable")]
        [SwaggerOperation("RorTable")]
        [Produces("application/json")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> RorTable()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("RorTable", true);
        }

        /// <response code="200">Returns list of saved views</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ListSavedViews")]
        [SwaggerOperation("ListSavedViews")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ListSavedViews()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ListSavedViews", true);
        }

        /// <response code="201">Returns the created saved view</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("CreateSavedView")]
        [SwaggerOperation("CreateSavedView")]
        [ProducesResponseType(typeof(object), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CreateSavedView()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("CreateSavedView", true, "POST");
        }

        /// <response code="200">Returns the saved views</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="404">If the saved view is not found</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("ReadSavedView")]
        [SwaggerOperation("ReadSavedView")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ReadSavedView()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("ReadSavedView", true);
        }

        /// <response code="200">Returns the updated saved views</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="404">If the saved view is not found</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("UpdateSavedView")]
        [SwaggerOperation("UpdateSavedView")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateSavedView()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("UpdateSavedView", true, "PUT");
        }

        /// <response code="200">Returns the updated saved views</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="404">If the saved view is not found</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("PartialUpdateSavedView")]
        [SwaggerOperation("PartialUpdateSavedView")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> PartialUpdateSavedView()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("PartialUpdateSavedView", true, "PATCH");
        }

        /// <response code="204">Returns nothing, delete success</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="404">If the saved view is not found</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DeleteSavedView")]
        [SwaggerOperation("DeleteSavedView")]
        [ProducesResponseType(typeof(object), StatusCodes.Status204NoContent)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> DeleteSavedView()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("DeleteSavedView", true, "DELETE");
        }

        /// <summary>
        /// Retrieve DashboardData Data.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api
        ///     {
        ///         "Component": "DashboardData",
        ///         "TenantGuid": "{{TenantGuid}}",
        ///         "Controller": "PXW/DataApi",
        ///         "Action": "DashboardData",
        ///         "PathParameters": {
        ///             "profile_id" : "{{profile_id}}",
        ///         },
        ///         "QueryParameters": {
        ///             "as_of_date": "{{as_of_date}}",
        ///             "currency": "CAD"
        ///         }
        ///     }
        /// </remarks>        
        /// <response code="200">Returns the successful response of the dashboard data of the selected profile.</response>
        /// <response code="400">If the input parameters are invalid</response>
        /// <response code="401">If the user is not authorized</response>
        /// <response code="500">If there is a server error</response>
        [HttpPost("DashboardData")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DashboardData()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("DashboardData", true, "GET");
        }
        
        /// <response code="204">Returns nothing, security code available</response>
        /// <response code="409">Security code conflict with existing one</response>
        [HttpPost("CheckSecurityCodeAvailability")]
        [SwaggerOperation("CheckSecurityCodeAvailability")]
        [ProducesResponseType(typeof(object), StatusCodes.Status204NoContent)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CheckSecurityCodeAvailability()
        {
            return await _baseService.FetchApiEndpointsDataWithOptionalToken("CheckSecurityCodeAvailability", true, "GET");
        }
        #endregion
    }
}
