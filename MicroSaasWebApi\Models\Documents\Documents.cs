﻿using MicroSaasWebApi.Models.AlphaCCO;

namespace MicroSaasWebApi.Models.PXW
{
    public class Documents
    {
    }
    public class GetDocumentsRequest : BaseAPIRequest
    {
        public required List<string> accountIds { get; set; }
    }
    public class DownloadDocumentRequest : BaseAPIRequest
    {
        public required string filename { get; set; }
    }
    public class DownloadDocumentsRequest : BaseAPIRequest
    {
        public required List<string> filenames { get; set; }
    }
    public class DocumentType
    {
        /// <summary>
        /// Name of the Document
        /// </summary>
        /// <example></example>
        public string? Name { get; set; }
        /// <summary>
        /// Display Name of the Document
        /// </summary>
        /// <example></example>
        public string? DisplayName { get; set; }
        /// <summary>
        /// NoDocumentMessage of the Document
        /// </summary>
        /// <example></example>
        public string? NoDocumentMessage { get; set; }
        /// <summary>
        /// Description of the Document
        /// </summary>
        /// <example></example>
        public string? Description { get; set; }
        /// <summary>
        /// Show if the document is Active
        /// </summary>
        /// <example>true</example>
        public bool IsActive { get; set; } = true;
    }
}
