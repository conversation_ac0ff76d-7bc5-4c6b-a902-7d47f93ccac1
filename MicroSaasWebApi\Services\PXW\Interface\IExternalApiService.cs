﻿using System.Net;
using System.Net.Http.Headers;

namespace MicroSaasWebApi.Services.PXW.Interface
{
    public interface IExternalApiService
    {
        Task<(bool success, object? result, string? errorMessage, HttpStatusCode statusCode)>
            GetThirdPartyDataAsync(string apiUrl, string httpMethod, string? jwtToken = "", Dictionary<string, string>? queryParametersDictionary = null,
            string? payloadString = null, string? userLanguage = "en");

        Task<HttpResponseMessage> GetThirdPartyDataAsyncNoTransform(
          string apiUrl,
          string httpMethod,
          string? jwtToken = "",
          Dictionary<string, string>? queryParametersDictionary = null,
          string? payloadString = null,
          string? pydioUserToken = null
        );
        Task<MultipartFormDataContent> PrepareFilteredFormDataAsync(HttpRequest request, string apiUrlString);
        Task<HttpResponseMessage> GetThirdPartyAsyncFilteredFormData(
            string apiUrlString,
            string httpMethod,
            string jwtToken,
            HttpRequest request,
            string? pydioUserToken = null);
        Task<HttpResponseMessage> GetThirdPartyAsyncFormData(string apiUrl, string httpMethod, string jwtToken, HttpRequest request, string? pydioUserToken = null);

        Task<HttpResponseMessage> GetFullResponseThirdPartyDataAsync(
          string apiUrl,
          string httpMethod,
          string? jwtToken = "",
          Dictionary<string, string>? queryParametersDictionary = null,
          string? payloadString = null,
          string? pydioUserToken = null
        );
    }


}
