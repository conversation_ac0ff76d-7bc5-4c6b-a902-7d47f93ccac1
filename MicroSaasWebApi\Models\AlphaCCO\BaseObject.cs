namespace MicroSaasWebApi.Models.AlphaCCO
{
    /// <summary>
    /// BaseObject holds basic properties, most needed/used for a Grid List.
    /// </summary>
    public class BaseObject
    {
        public Guid MasterID { get; set; }
        public int Id { get; set; }
        public string? Name { get; set; }
        public bool IsActive { get; set; }
        public int SortOrder { get; set; }
        public bool IsFeatured { get; set; }
    }
}