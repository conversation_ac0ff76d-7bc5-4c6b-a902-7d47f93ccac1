﻿//using AdvantageCMS.Core.Common;
//using AdvantageCMS.Core.Utils;
//using AdvantageCMS.Data;
using Newtonsoft.Json;
using MicroSaasWebApi.Models.AlphaCCO;
using MicroSaasWebApi.Models.Profile;
using MicroSaasWebApi.Models.PXW.AppSettings;
using MicroSaasWebApi.Services.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
//using System.Web.Script.Serialization;
using RestSharp;

namespace MicroSaasWebApi.Services.AlphaCCO.PXW
{
    /// <summary>
    /// Summary description for WorkflowEngine
    /// </summary>
    public class WorkflowEngine : EngineBase
    {
        #region Initialize              
        public WorkflowEngine(int domainId, int languageId, IApplicationSettingsService applicationSettingsService, IHttpContextAccessor httpContextAccessor) : base(domainId, languageId, applicationSettingsService, httpContextAccessor) { }
        #endregion Initialize

        private AlphaCCOWorkflows _workflows;
        public AlphaCCOWorkflows Workflows
        {
            get
            {
                if (_workflows == null)
                {
                    //_workflows = ModuleEngine.GetAllPublishedObjects<AlphaCCOWorkflow>();
                    _applicationSettingsService.SetAlphaCCOWorkflowsInHttpContext();
                    _workflows = _applicationSettingsService.GetAlphaCCOWorkflows();
                }
                return _workflows;
            }
        }

        public List<AlphaCCOWorkflowResponse> StartWorkFlow(AccountMember member, AccountProfile ap, AlphaCCOWorkflow workflow, string openAccountName, List<Override> overrides)
        {
            List<AlphaCCOWorkflowResponse> workflows = new List<AlphaCCOWorkflowResponse>();
            AlphaCCoWorkflowStartResponse sResult = AlphaCCoWorkflowStart(member, ap, workflow, openAccountName, overrides);

            if (sResult != null)
            {
                if (sResult.workflowInstanceKey != null)
                {
                    int milliseconds = 3000;
                    Thread.Sleep(milliseconds);
                    workflows = GetAlphaCCOWorkflows(ap, workflow.WorkflowKey, sResult.workflowInstanceKey, overrides);
                    return workflows;
                }
                else
                {
                    List<AlphaCCOWorkflowResponse> result = new List<AlphaCCOWorkflowResponse>();
                    AlphaCCOWorkflowResponse res = new AlphaCCOWorkflowResponse();
                    res.statusCode = sResult.StatusCode;
                    res.statusDescription = sResult.StatusString;
                    result.Add(res);
                    return result;
                }
            }
            else
            {
                return null;
            }

        }


        public AccountMember UpdateUserWorkFlowsFromAlphaCCO(AccountMember member, AccountProfile prof)
        {
            if (member != null)
            {

                List<AlphaCCOWorkflowResponse> workflows = GetAlphaCCOWorkflows(prof, string.Empty, string.Empty, null);
                if (workflows != null && workflows.Count > 0)
                    prof.Workflows = workflows;
                else
                {

                    ///this member do not have any workflow before, create a new workflow for the default one
                    ///get the default survey
                    ///
                    //AlphaCCOWorkflow defaultWorkflow = Workflows.FirstOrDefault<AlphaCCOWorkflow>(w => w.IsMandatory && w.RequiredParentWorkflowID != null);
                    //if (defaultWorkflow != null)
                    //{
                    //    member.Workflows = StartWorkFlow(member, defaultWorkflow.WorkflowKey);
                    //}
                    prof.Workflows = new List<AlphaCCOWorkflowResponse>();

                }


                var tmp = new AccountProfile();
                var index = -1;
                try
                {
                    tmp = member.Account.Profiles.Where(a => a.ExternalId == prof.ExternalId).FirstOrDefault();
                    index = member.Account.Profiles.IndexOf(tmp);
                }
                catch
                {
                    index = -1;
                }

                if (index != -1)
                    member.Account.Profiles[index] = prof;
                else
                    member.Account.Profiles.Add(prof);

                //try
                //{
                //    member.LastModifiedDate = DateTime.Now;
                //    string message = string.Empty;
                //    var status = member.ValidateLockStatus(member, base.CurrentSql, base.DomainId, base.LanguageId, out message);

                //    if (status.Status == AdvantageCMS.Core.Admin.Event.eCMSEngineEventStatus.Success)
                //        ModuleEngine.BusinessHelper.PublishObject(ClientConstants.ServiceAccount, member);
                //    else
                //    {
                //        IdentityExtension.ResetCurrentSignedInAccountMember();
                //        member = IdentityExtension.CurrentSignedInAccountMember(this.CurrentPage);
                //        AccountProfile profile = member.Account.Profiles.FirstOrDefault(a => a.Name.Equals(prof.Name));

                //        UpdateUserWorkFlowsFromAlphaCCO(member, profile);
                //    }
                //}
                //catch (Exception ex)
                //{

                //}
                return member;
            }
            return null;

        }
        protected AlphaCCOAuthenticationResponse LoginResult;
        protected AlphaCCOExternalPartyResponse MemberResult;
        protected List<AlphaCCOWorkflowResponse> WorkflowResponse;
        protected AlphaCCoWorkflowStartResponse WorkflowStartResponse;


        public string GetActiveCurrency()
        {
            try
            {
                return _httpContextAccessor.HttpContext.Items["ActiveAccountProfileId"] as string;
            }
            catch
            {
                return "";
            }
        }

        public void SetActiveCurrency(string currency)
        {
            _httpContextAccessor.HttpContext.Items["ActiveAccountProfileCurrency"] = currency;
        }

        public string GetSelectedDate()
        {
            try
            {
                return _httpContextAccessor.HttpContext.Items["ActiveAccountProfileSelectedDate"] as string;
            }
            catch
            {
                return "";
            }
        }
        public void SetWorkflowKey(string WorkflowKey)
        {
            _httpContextAccessor.HttpContext.Items["WorkflowKey"] = WorkflowKey;
        }

        public string GetWorkflowKey()
        {
            try
            {
                return _httpContextAccessor?.HttpContext?.Items["WorkflowKey"] as string;
            }
            catch
            {
                return "";
            }
        }

        public void SetWorkflowInstanceId(string workflowInstanceId)
        {
            _httpContextAccessor.HttpContext.Items["workflowInstanceId"] = workflowInstanceId;
        }

        public string GetWorkflowInstanceId()
        {
            try
            {
                return _httpContextAccessor.HttpContext.Items["workflowInstanceId"] as string;
            }
            catch
            {
                return "";
            }
        }

        public void SetSelectedDate(string date)
        {
            _httpContextAccessor.HttpContext.Items["ActiveAccountProfileSelectedDate"] = date;
        }

        public void SetRefreshPowerBiReport(bool value)
        {
            _httpContextAccessor.HttpContext.Items["RefreshPowerBiReport"] = value.ToString();
        }
        public string GetRefreshPowerBiReport()
        {
            try
            {
                return _httpContextAccessor.HttpContext.Items["RefreshPowerBiReport"] as string;
            }
            catch
            {
                return bool.FalseString;
            }
        }

        #region ServiceCalls

        private async Task<AlphaCCOAuthenticationResponse> AlphaCCOAuthenticationRun()
        {
            try
            {
                if (_httpContextAccessor.HttpContext.Items["RefreshPowerBiReport"] != null)
                {
                    //LoginResult = (AlphaCCOAuthenticationResponse)HttpContext.Current.Session["AlphaCCOAuthentication"];
                    LoginResult = _httpContextAccessor.HttpContext.Items["AlphaCCOAuthentication"] as AlphaCCOAuthenticationResponse;
                }

                if (LoginResult == null)
                {
                    await AlphaCCOAuthentication();
                    //if (HttpContext.Current.Session != null)
                    //    HttpContext.Current.Session["AlphaCCOAuthentication"] = LoginResult;
                    if (_httpContextAccessor.HttpContext.Items["AlphaCCOAuthentication"] != null)
                        _httpContextAccessor.HttpContext.Items["AlphaCCOAuthentication"] = LoginResult;
                }
                return LoginResult;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        private async Task AlphaCCOAuthentication()
        {

            string path = string.Format("{0}/account/authenticate", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL);
            var client = new RestClient(path);
            var request = new RestRequest();
            request.Method = Method.Post;//Method.POST

            request.AddHeader("alphacco-tenant-id", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOTenantID);
            AlphaCCOLogin login = new AlphaCCOLogin { userName = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOHost, password = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOHostkey };
            string body = JsonConvert.SerializeObject(login);
            request.AddParameter("application/json", body, ParameterType.RequestBody);
            try
            {
                RestResponse response = await client.ExecuteAsync(request);
                if (!response.IsSuccessful)
                {
                    string er = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                    LoginResult = new AlphaCCOAuthenticationResponse();
                    LoginResult.StatusCode = response.StatusCode.ToString();
                    LoginResult.StatusString = response.ErrorMessage + response.Content;
                }

                LoginResult = DeserializeAuthenticationResponse(response.Content);
            }
            catch (Exception ex)
            {
                LoginResult = new AlphaCCOAuthenticationResponse();
                LoginResult.StatusCode = ex.GetHashCode().ToString();
                LoginResult.StatusString = ex.Message;
            }
        }

        private async Task AlphaCCOAuthenticationWithExternalId(string externalId)
        {

            string path = string.Format("{0}/account/authenticate", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL);
            var client = new RestClient(path);
            var request = new RestRequest();
            request.Method = Method.Post;//Method.POST

            request.AddHeader("alphacco-tenant-id", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOTenantID);
            request.AddHeader("alphacco-user-context-id", externalId);
            AlphaCCOLogin login = new AlphaCCOLogin { userName = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOHost, password = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOHostkey };
            string body = JsonConvert.SerializeObject(login);
            request.AddParameter("application/json", body, ParameterType.RequestBody);
            try
            {
                RestResponse response = await client.ExecuteAsync(request);
                if (!response.IsSuccessful)
                {
                    string er = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                    LoginResult = new AlphaCCOAuthenticationResponse();
                    LoginResult.StatusCode = response.StatusCode.ToString();
                    LoginResult.StatusString = response.ErrorMessage + response.Content;
                }

                LoginResult = DeserializeAuthenticationResponse(response.Content);
            }
            catch (Exception ex)
            {
                LoginResult = new AlphaCCOAuthenticationResponse();
                LoginResult.StatusCode = ex.GetHashCode().ToString();
                LoginResult.StatusString = ex.Message;
            }
        }

        public AccountMember GetAlphaCCOAccount(AccountMember member, AccountProfile prof)
        {
            try
            {
                if (prof == null) return null;
                GetAlphaCCOAccountTask(member, prof).Wait();
                if (member.Account == null) member.Account = new AccountInformation();
                if (member.Account.Profiles == null) member.Account.Profiles = new List<AccountProfile>();
                if (MemberResult != null && MemberResult.key != null)
                {

                    prof.Email = MemberResult.email;
                    prof.FirstName = MemberResult.firstName;
                    prof.LastName = MemberResult.lastName;
                    prof.AlphaCCOKey = MemberResult.key;
                    var tmp = new AccountProfile();
                    var index = -1;
                    try
                    {
                        tmp = member.Account.Profiles.Where(a => a.ExternalId == prof.ExternalId).FirstOrDefault();
                        index = member.Account.Profiles.IndexOf(tmp);
                    }
                    catch
                    {
                        index = -1;
                    }

                    if (index != -1)
                        member.Account.Profiles[index] = prof;
                    else
                        member.Account.Profiles.Add(prof);
                    //member.PortalUserId = MemberResult.externalSystemId;
                    //member.PreferredLanguage = MemberResult.language;


                }

                member = UpdateUserWorkFlowsFromAlphaCCO(member, prof);
                return member;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public AlphaCCOExternalPartyResponse GetAlphaCCOAccountByWorkflowKey(string key)
        {
            try
            {
                if (string.IsNullOrEmpty(key)) return null;
                GetAlphaCCOAccountByWorkflowKeyTask(key).Wait();
                return MemberResult;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        private async Task GetAlphaCCOAccountByWorkflowKeyTask(string key)
        {
            if (LoginResult == null)
            {
                await AlphaCCOAuthenticationRun();
            }

            string path = string.Format("{0}/externalparty/{1}", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL, key);
            var client = new RestClient(path);

            var request = new RestRequest();
            request.Method = Method.Get;//Method.GET

            request.AddHeader("alphacco-tenant-id", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOTenantID);
            request.AddHeader("Authorization", string.Format("Bearer {0}", LoginResult.token));

            try
            {
                RestResponse response = client.Execute(request);
                if (!response.IsSuccessful || response.Content.Contains("ERROR"))
                {
                    string er = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                    MemberResult = new AlphaCCOExternalPartyResponse();
                    MemberResult.StatusCode = response.StatusCode.ToString();
                    MemberResult.StatusString = response.ErrorMessage == null ? response.Content : response.ErrorMessage;

                }

                MemberResult = DeserializeExternalPartyResponse(response.Content);
            }
            catch (Exception ex)
            {
                MemberResult = new AlphaCCOExternalPartyResponse();
                MemberResult.StatusCode = ex.GetHashCode().ToString();
                MemberResult.StatusString = ex.Message;
            }
        }
        private async Task GetAlphaCCOAccountTask(AccountMember member, AccountProfile ap)
        {
            if (LoginResult == null)
            {
                await AlphaCCOAuthenticationRun();
            }

            string path = string.Format("{0}/externalparty", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL);
            var client = new RestClient(path);

            var request = new RestRequest();
            request.Method = Method.Put;//Method.PUT

            request.AddHeader("alphacco-tenant-id", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOTenantID);
            request.AddHeader("Authorization", string.Format("Bearer {0}", LoginResult.token));
            AlphaCCOExternalParty body = new AlphaCCOExternalParty();
            body.firstName = ap.FirstName;
            body.lastName = ap.LastName;
            body.email = ap.Email;
            body.language = ap.PreferredLanguage;
            body.isactive = ap.IsActive ? "true" : "false";
            if (!string.IsNullOrEmpty(ap.ExternalId))
            {
                body.externalSystemId = ap.ExternalId;

            }

            string requeststr = JsonConvert.SerializeObject(body);
            request.AddParameter("application/json", requeststr, ParameterType.RequestBody);

            try
            {
                RestResponse response = client.Execute(request);
                if (!response.IsSuccessful || response.Content.Contains("ERROR"))
                {
                    string er = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                    MemberResult = new AlphaCCOExternalPartyResponse();
                    MemberResult.StatusCode = response.StatusCode.ToString();
                    MemberResult.StatusString = response.ErrorMessage == null ? response.Content : response.ErrorMessage;

                }

                MemberResult = DeserializeExternalPartyResponse(response.Content);
            }
            catch (Exception ex)
            {
                MemberResult = new AlphaCCOExternalPartyResponse();
                MemberResult.StatusCode = ex.GetHashCode().ToString();
                MemberResult.StatusString = ex.Message;
            }
        }

        //public List<AlphaCCOWorkflowResponse> GetAlphaCCOWorkflowsForExistWorkFlow(AccountProfile prof, string workflowKey)
        //{
        //    try
        //    {
        //        //AccountProfile ap = GetActiveAccount(member);

        //        GetAlphaCCOWorkflowsTask(prof, workflowKey, string.Empty).Wait();
        //        Thread.Sleep(3000);
        //        GetAlphaCCOWorkflowsTask(prof, workflowKey, string.Empty).Wait();
        //        return WorkflowResponse ?? new List<AlphaCCOWorkflowResponse>();
        //    }
        //    catch (Exception ex)
        //    {
        //        return null;
        //    }
        //}

        public List<AlphaCCOWorkflowResponse> GetAlphaCCOWorkflows(AccountProfile prof, string workflowKey, string workflowInstanceId, List<Override> dataOverrides)
        {
            try
            {
                //AccountProfile ap = GetActiveAccount(member);
                if (!string.IsNullOrEmpty(workflowInstanceId))
                {
                    GetAlphaCCOWorkflowsTask(prof, workflowKey, workflowInstanceId, dataOverrides).Wait();
                    Thread.Sleep(3000);
                    GetAlphaCCOWorkflowsTask(prof, workflowKey, string.Empty, dataOverrides).Wait();
                }
                else
                {

                    GetAlphaCCOWorkflowsTask(prof, workflowKey, string.Empty, dataOverrides).Wait();

                    //Thread.Sleep(3000);
                    //GetAlphaCCOWorkflowsTask(prof, workflowKey, string.Empty, dataOverrides).Wait();

                }

                return WorkflowResponse ?? new List<AlphaCCOWorkflowResponse>();
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        private async Task GetAlphaCCOWorkflowsTask(AccountProfile profile, string workflowKey, string workflowInstanceId, List<Override> dataOverrides)
        {
            if (LoginResult == null)
            {
                await AlphaCCOAuthenticationRun();
            }

            if (LoginResult.token != null)
            {
                string path = string.Format("{0}/workflow/search", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL);
                var client = new RestClient(path);

                var request = new RestRequest();
                request.Method = Method.Post;//Method.POST
                request.AddHeader("alphacco-tenant-id", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOTenantID);
                request.AddHeader("Authorization", string.Format("Bearer {0}", LoginResult.token));

                AlphaCCOSearch body = new AlphaCCOSearch();
                body.externalPartyKey = profile.AlphaCCOKey;
                // body.userKey = profile.AlphaCCOKey;
                if (!string.IsNullOrEmpty(workflowKey))
                    body.workflowkey = workflowKey;
                if (!string.IsNullOrEmpty(workflowInstanceId))
                    body.workflowInstancekey = workflowInstanceId;
                //if (dataOverrides != null && dataOverrides.Count > 0)
                //    body.dataTagOverrides = dataOverrides;
                string requeststr = JsonConvert.SerializeObject(body);
                //var body = string.Format("{{ workflowkey: null,workflowInstancekey: null,externalPartyKey: \"{1}\" }}", string.IsNullOrEmpty(workflowKey) ? null : workflowKey, alphaccokey);
                request.AddParameter("application/json", requeststr, ParameterType.RequestBody);
                try
                {
                    RestResponse response = client.Execute(request);
                    if (!(response.IsSuccessful || response.StatusCode.ToString() == "NotFound"))
                    {
                        string er = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                        WorkflowResponse = null;
                    }

                    WorkflowResponse = DeserializeWorkflowResponse(response.Content);
                }
                catch (Exception ex)
                {
                    WorkflowResponse = null;
                }
            }
            else
            {
                WorkflowResponse = null;
            }
        }
        public List<AlphaCCOWorkflowResponse> StopAlphaCCOWorkflow(AccountProfile prof, string workflowKey, string workflowInstanceId, string reason)
        {
            try
            {
                //AccountProfile ap = GetActiveAccount(member);
                if (!string.IsNullOrEmpty(workflowInstanceId))
                {
                    StopAlphaCCOWorkflowsTask(prof, workflowKey, workflowInstanceId, reason).Wait();
                }
                else
                {
                    return null;
                }

                return WorkflowResponse ?? new List<AlphaCCOWorkflowResponse>();
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        private async Task StopAlphaCCOWorkflowsTask(AccountProfile profile, string workflowKey, string workflowInstanceId, string reason)
        {
            if (LoginResult == null)
            {
                await AlphaCCOAuthenticationRun();
            }

            if (LoginResult.token != null)
            {
                string path = string.Format("{0}/workflow/stop", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL);
                var client = new RestClient(path);

                var request = new RestRequest();
                request.Method = Method.Post;//Method.POST
                request.AddHeader("alphacco-tenant-id", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOTenantID);
                request.AddHeader("Authorization", string.Format("Bearer {0}", LoginResult.token));

                AlphaCCOSearch body = new AlphaCCOSearch();
                body.workflowInstancekey = workflowInstanceId;
                body.reason = reason;
                body.userKey = profile.AlphaCCOKey;
                string requeststr = JsonConvert.SerializeObject(body);
                //var body = string.Format("{{ workflowkey: null,workflowInstancekey: null,externalPartyKey: \"{1}\" }}", string.IsNullOrEmpty(workflowKey) ? null : workflowKey, WorkflowKey);
                request.AddParameter("application/json", requeststr, ParameterType.RequestBody);
                try
                {
                    RestResponse response = client.Execute(request);
                    if (!(response.IsSuccessful || response.StatusCode.ToString() == "NotFound"))
                    {
                        string er = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                        WorkflowResponse = null;
                    }

                    WorkflowResponse = DeserializeWorkflowResponse(response.Content);
                }
                catch (Exception ex)
                {
                    WorkflowResponse = null;
                }
            }
            else
            {
                WorkflowResponse = null;
            }
        }
        public AlphaCCoWorkflowStartResponse AlphaCCoWorkflowStart(AccountMember member, AccountProfile ap, AlphaCCOWorkflow workflow, string openAccountName, List<Override> dataOverrides)
        {
            try
            {
                if (string.IsNullOrEmpty(openAccountName))
                    AlphaCCoWorkflowStartTask(ap, workflow, ap.FirstName + " " + ap.LastName, dataOverrides).Wait();
                else
                    AlphaCCoWorkflowStartTask(ap, workflow, openAccountName + " " + ap.FirstName + " " + ap.LastName, dataOverrides).Wait();
                return WorkflowStartResponse;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        private async Task AlphaCCoWorkflowStartTask(AccountProfile ap, AlphaCCOWorkflow workflow, string name, List<Override> dataOverrides)
        {
            if (LoginResult == null)
            {
                await AlphaCCOAuthenticationRun();
            }

            if (LoginResult.token != null)
            {
                string path = string.Format("{0}/workflow/start", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL);
                var client = new RestClient(path);


                var request = new RestRequest();
                request.Method = Method.Post;//Method.POST

                request.AddHeader("alphacco-tenant-id", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOTenantID);
                request.AddHeader("Authorization", string.Format("Bearer {0}", LoginResult.token));
                AlphaCCoWorkflowStart body = new AlphaCCoWorkflowStart();
                body.externalPartyKey = ap.AlphaCCOKey;
                body.workflowkey = workflow.WorkflowKey;

                List<string> instanceIds = new List<string>();

                ///First if need prevoius one
                /// This code is commented till we have further details of multiple cards.
                //if (workflow.IsPOACWorkflow)
                //{
                //    GetLatestCompletedWorkflowInstances(ap, workflow.WorkflowIDstoSearchOnCompletedStatus, ref instanceIds);
                //    if (instanceIds.Count > 0 && workflow.IsNeedParentWorkflowInstanceID)
                //    {
                //        GetAllParentWorkflowInstances(ap, workflow, ref instanceIds);
                //        body.linkedWorkflows = instanceIds.ToArray();
                //    }
                //    else
                //    {
                //        body.linkedWorkflows = instanceIds.ToArray();
                //    }
                //}

                //if (instanceIds.Count == 0 && workflow.WorkflowIDtoSearchOnCompletedStatus != null && workflow.WorkflowIDtoSearchOnCompletedStatus != Guid.Empty)
                //{
                //    GetCompletedWorkflowInstances(ap, workflow.WorkflowIDtoSearchOnCompletedStatus, ref instanceIds);


                //    //need add all Parent workflow InstanceIds when start , changes for 
                //    if (instanceIds.Count > 0 && workflow.IsNeedParentWorkflowInstanceID)
                //    {
                //        GetAllParentWorkflowInstances(ap, workflow, ref instanceIds);
                //        body.linkedWorkflows = instanceIds.ToArray();
                //    }
                //    else
                //    {
                //        body.linkedWorkflows = instanceIds.ToArray();
                //    }

                //}

                //if (instanceIds.Count == 0 && workflow.IsNeedPrevoiusWorkflowInstanceID)
                //{
                //    GetPreviousCompletedWorkflowInstances(ap, workflow, ref instanceIds);
                //    body.linkedWorkflows = instanceIds.ToArray();

                //    if (instanceIds.Count > 0 && workflow.IsNeedParentWorkflowInstanceID)
                //    {
                //        GetAllParentWorkflowInstances(ap, workflow, ref instanceIds);
                //        body.linkedWorkflows = instanceIds.ToArray();
                //    }
                //    else
                //    {
                //        body.linkedWorkflows = instanceIds.ToArray();
                //    }
                //}

                //if (instanceIds.Count == 0 && workflow.IsNeedParentWorkflowInstanceID)
                //{
                //    GetAllParentWorkflowInstances(ap, workflow, ref instanceIds);
                //    body.linkedWorkflows = instanceIds.ToArray();
                //}

                //if (dataOverrides != null && dataOverrides.Count > 0)
                //    body.dataTagOverrides = dataOverrides;

                body.name = name;
                string requeststr = JsonConvert.SerializeObject(body);
                //var body = string.Format("{{ workflowKey: \"{0}\",externalPartyKey: \"{1}\",name: \"{2}\" }}", string.IsNullOrEmpty(workflowKey) ? null : workflowKey, alphaccokey, name);
                request.AddParameter("application/json", requeststr, ParameterType.RequestBody);
                try
                {
                    RestResponse response = client.Execute(request);
                    if (!response.IsSuccessful)
                    {
                        string er = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                        WorkflowStartResponse = new AlphaCCoWorkflowStartResponse();
                        WorkflowStartResponse.StatusCode = response.StatusCode.ToString();
                        WorkflowStartResponse.StatusString = response.ErrorMessage + response.Content;
                    }
                    else
                    {
                        WorkflowStartResponse = DeserializeWorkflowStartResponse(response.Content);
                    }
                }
                catch (Exception ex)
                {
                    WorkflowStartResponse = new AlphaCCoWorkflowStartResponse();
                    WorkflowStartResponse.StatusCode = ex.GetHashCode().ToString();
                    WorkflowStartResponse.StatusString = ex.Message;
                }
            }
            else
            {
                WorkflowStartResponse = new AlphaCCoWorkflowStartResponse();
                WorkflowStartResponse.StatusCode = LoginResult.StatusCode.ToString();
                WorkflowStartResponse.StatusString = LoginResult.StatusString;
            }

        }
        public async Task<string> AlphaCCOQuestionsBySurveyId(AccountProfile accountProfile, string surveyId)
        {
            object result = null;

            if (LoginResult == null)
            {
                await AlphaCCOAuthenticationWithExternalId(accountProfile.AlphaCCOKey);
            }

            if (LoginResult.token != null)
            {
                string path = string.Format("{0}/survey/{1}", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL, surveyId);
                var client = new RestClient(path);

                var request = new RestRequest();
                request.Method = Method.Get;

                request.AddHeader("alphacco-tenant-id", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOTenantID);
                request.AddHeader("Authorization", string.Format("Bearer {0}", LoginResult.token));
                AlphaCCoWorkflowStart body = new AlphaCCoWorkflowStart();
                string requeststr = JsonConvert.SerializeObject(body);
                request.AddParameter("application/json", requeststr, ParameterType.RequestBody);
                try
                {
                    var response = await client.ExecuteAsync(request);

                    if (!response.IsSuccessful && response.Content != null)
                    {
                        string er = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                        WorkflowStartResponse = new AlphaCCoWorkflowStartResponse();
                        WorkflowStartResponse.StatusCode = response.StatusCode.ToString();
                        WorkflowStartResponse.StatusString = response.ErrorMessage + response.Content;

                        // Setting WorkflowStartResponse as result.
                        result = WorkflowStartResponse;
                    }
                    else
                    {
                        // Deserialize JSON response to dynamic object.
                        var data = JsonConvert.DeserializeObject<dynamic>(response?.Content);

                        // Setting data as result.
                        result = data;
                    }
                }
                catch (Exception ex)
                {
                    WorkflowStartResponse = new AlphaCCoWorkflowStartResponse();
                    WorkflowStartResponse.StatusCode = ex.GetHashCode().ToString();
                    WorkflowStartResponse.StatusString = ex.Message;

                    // Setting WorkflowStartResponse as result.
                    result = WorkflowStartResponse;
                }
            }
            else
            {
                WorkflowStartResponse = new AlphaCCoWorkflowStartResponse();
                WorkflowStartResponse.StatusCode = LoginResult.StatusCode.ToString();
                WorkflowStartResponse.StatusString = LoginResult.StatusString;

                // Setting WorkflowStartResponse as result.
                result = WorkflowStartResponse;
            }
            // Serialize to JSON and return.
            return JsonConvert.SerializeObject(result);
        }

        public async Task<string> SaveAlphaCCOQuestionsBySurveyId(AccountProfile ap, string bodyJson)
        {
            object result = null;

            if (LoginResult == null)
            {
                await AlphaCCOAuthenticationWithExternalId(ap.AlphaCCOKey);
            }

            if (LoginResult.token != null)
            {
                string path = string.Format("{0}/survey", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL);
                var client = new RestClient(path);

                var request = new RestRequest();
                request.Method = Method.Put;

                request.AddHeader("alphacco-tenant-id", FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOTenantID);
                request.AddHeader("Authorization", string.Format("Bearer {0}", LoginResult.token));

                request.AddParameter("application/json", bodyJson, ParameterType.RequestBody);
                try
                {
                    var response = await client.ExecuteAsync(request);

                    if (!response.IsSuccessful && response.Content != null)
                    {
                        string er = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                        WorkflowStartResponse = new AlphaCCoWorkflowStartResponse();
                        WorkflowStartResponse.StatusCode = response.StatusCode.ToString();
                        WorkflowStartResponse.StatusString = response.ErrorMessage + response.Content;

                        // Setting WorkflowStartResponse as result.
                        result = WorkflowStartResponse;
                    }
                    else
                    {
                        // Deserialize JSON response to dynamic object.
                        var data = JsonConvert.DeserializeObject<dynamic>(response?.Content);

                        // Setting data as result.
                        result = data;
                    }
                }
                catch (Exception ex)
                {
                    WorkflowStartResponse = new AlphaCCoWorkflowStartResponse();
                    WorkflowStartResponse.StatusCode = ex.GetHashCode().ToString();
                    WorkflowStartResponse.StatusString = ex.Message;

                    // Setting WorkflowStartResponse as result.
                    result = WorkflowStartResponse;
                }
            }
            else
            {
                WorkflowStartResponse = new AlphaCCoWorkflowStartResponse();
                WorkflowStartResponse.StatusCode = LoginResult.StatusCode.ToString();
                WorkflowStartResponse.StatusString = LoginResult.StatusString;

                // Setting WorkflowStartResponse as result.
                result = WorkflowStartResponse;
            }
            // Serialize to JSON and return.
            return JsonConvert.SerializeObject(result);
        }


        private List<string> GetLatestCompletedWorkflowInstances(AccountProfile ap, List<string> workflowIds, ref List<string> instanceIds)
        {

            List<string> inte = instanceIds;
            AlphaCCOWorkflows workflowsToSearch = (AlphaCCOWorkflows)Workflows.Where(w => workflowIds.Contains(w.MasterID.ToString())).ToList();
            if (workflowsToSearch != null)
            {
                List<string> ids = workflowsToSearch.Select(w => w.WorkflowKey.ToString()).ToList();
                AlphaCCOWorkflowResponse completedparentworkflow = ap.Workflows.OrderByDescending(w => w.endDate).FirstOrDefault(w => ids.Contains(w.workflowKey) && w.statusCode == "C");
                if (completedparentworkflow != null)
                {
                    if (completedparentworkflow.startedByWorkflowInstanceKey != null)
                    {
                        inte.Add(completedparentworkflow.startedByWorkflowInstanceKey.ToString());
                    }
                    inte.Add(completedparentworkflow.workflowInstanceKey);

                }

            }
            return inte;
        }
        private List<string> GetAllParentWorkflowInstances(AccountProfile ap, AlphaCCOWorkflow workflow, ref List<string> rtn)
        {
            if (workflow.RequiredParentWorkflowID != null)
            {
                List<string> inte = rtn;
                AlphaCCOWorkflow parentworkflow = Workflows.FirstOrDefault(w => w.MasterID.Equals(workflow.RequiredParentWorkflowID));
                if (parentworkflow != null)
                {
                    AlphaCCOWorkflowResponse completedparentworkflow = ap.Workflows.OrderByDescending(w => w.endDate).FirstOrDefault(w => w.workflowKey.Equals(parentworkflow.WorkflowKey.ToString()) && w.statusCode == "C");
                    if (completedparentworkflow != null)
                    {
                        inte.Add(completedparentworkflow.workflowInstanceKey);

                    }
                    rtn = inte;

                    GetAllParentWorkflowInstances(ap, parentworkflow, ref rtn);
                }
            }
            return rtn;
        }
        private List<string> GetCompletedWorkflowInstances(AccountProfile ap, Guid workflowKey, ref List<string> rtn)
        {

            List<string> inte = rtn;
            AlphaCCOWorkflow workflow = Workflows.FirstOrDefault(w => w.MasterID.Equals(workflowKey));
            AlphaCCOWorkflowResponse completedparentworkflow;

            completedparentworkflow = ap.Workflows.OrderByDescending(w => w.endDate).FirstOrDefault(w => w.workflowKey.Equals(workflow.WorkflowKey.ToString()) && w.statusCode == "C");

            if (completedparentworkflow != null)
            {
                inte.Add(completedparentworkflow.workflowInstanceKey);
            }
            rtn = inte;

            return rtn;
        }
        private List<string> GetPreviousCompletedWorkflowInstances(AccountProfile ap, AlphaCCOWorkflow workflow, ref List<string> rtn)
        {

            List<string> inte = rtn;

            AlphaCCOWorkflowResponse completedparentworkflow;

            completedparentworkflow = ap.Workflows.OrderByDescending(w => w.endDate).FirstOrDefault(w => w.workflowKey.Equals(workflow.WorkflowKey.ToString()) && w.statusCode == "C");

            if (completedparentworkflow != null)
            {
                inte.Add(completedparentworkflow.workflowInstanceKey);
            }
            rtn = inte;

            return rtn;
        }
        private AlphaCCoWorkflowStartResponse DeserializeWorkflowStartResponse(string json)
        {
            AlphaCCoWorkflowStartResponse obj;
            //JavaScriptSerializer oJS = new JavaScriptSerializer();
            obj = JsonConvert.DeserializeObject<AlphaCCoWorkflowStartResponse>(json);
            return obj;
        }
        private List<AlphaCCOWorkflowResponse> DeserializeWorkflowResponse(string json)
        {
            List<AlphaCCOWorkflowResponse> obj;
            //JavaScriptSerializer oJS = new JavaScriptSerializer();
            obj = JsonConvert.DeserializeObject<List<AlphaCCOWorkflowResponse>>(json);
            return obj;
        }
        private AlphaCCOAuthenticationResponse DeserializeAuthenticationResponse(string json)
        {
            AlphaCCOAuthenticationResponse obj;
            //JavaScriptSerializer oJS = new JavaScriptSerializer();
            obj = JsonConvert.DeserializeObject<AlphaCCOAuthenticationResponse>(json);
            return obj;
        }
        private AlphaCCOExternalPartyResponse DeserializeExternalPartyResponse(string json)
        {
            AlphaCCOExternalPartyResponse obj;
            //JavaScriptSerializer oJS = new JavaScriptSerializer();
            obj = JsonConvert.DeserializeObject<AlphaCCOExternalPartyResponse>(json);
            return obj;
        }
        #endregion

        //public void AlphaCCOLogin(AccountMember ac)
        //{
        //    ac = GetAlphaCCOAccount(ac, GetActiveProfile(ac));

        //}

    }
}