﻿using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace SharePointAPI
{
    public static class General
    {
        //public static string SiteUrl = System.Configuration.ConfigurationManager.AppSettings["SharePointUrl"].ToString();
        //public static string ClientId = System.Configuration.ConfigurationManager.AppSettings["SharePointClientId"].ToString();
        //public static string ClientSecret = System.Configuration.ConfigurationManager.AppSettings["SharePointClientSecret"].ToString();
        //public static string DocumentLibrary = System.Configuration.ConfigurationManager.AppSettings["DocumentLibrary"].ToString();
        //public static string ClientSubFolder = System.Configuration.ConfigurationManager.AppSettings["ClientSubFolder"].ToString();

        //public static string DocumentLibraryDest = System.Configuration.ConfigurationManager.AppSettings["DocumentLibraryDest"].ToString();
        //public static string ClientSubFolderDest = System.Configuration.ConfigurationManager.AppSettings["ClientSubFolderDest"].ToString();

        public static string SiteUrl { get; set; }
        public static string ClientId { get; set; }
        public static string ClientSecret { get; set; }
        public static string DocumentLibrary { get; set; }
        public static string ClientSubFolder { get; set; }

        public static string DocumentLibraryDest { get; set; }
        public static string ClientSubFolderDest { get; set; }



        public static void SharePointAPISetting(string siteURL, string clientId, string clientSecret, string documentLibrary, string clientSubfolder,
            string documentLibraryDest, string clientSubFolderDest)
        {
            SiteUrl = siteURL;
            ClientId = clientId;
            ClientSecret = clientSecret;
            DocumentLibrary = documentLibrary;
            ClientSubFolder = clientSubfolder;
            DocumentLibraryDest = documentLibraryDest;
            ClientSubFolderDest = clientSubFolderDest;
        }


        public static string CreateCAMLQuery(List<string> parameters, string orAndCondition, bool isIncludeWhereClause, string fname)
        {
            StringBuilder sb = new StringBuilder();
            if (parameters.Count == 0)
            {
                AppendEQ(sb, "all");
            }
            int j = 0;
            if (parameters.Count > 0)
            {
                for (int i = 0; i < parameters.Count; i++)
                {
                    if (!string.IsNullOrEmpty(parameters[i].Split(';')[3]))
                    {
                        if (parameters[i].Split(';')[2] == "Eq")
                        {
                            AppendEQ(sb, parameters[i]);
                            if (i > 0 && j > 0)
                            {
                                sb.Insert(0, "<" + orAndCondition + ">");
                                sb.Append("</" + orAndCondition + ">");
                            }
                            j++;
                        }
                        else if (parameters[i].Split(';')[2] == "In")
                        {
                            AppendIn(sb, parameters[i]);
                            if (i > 0 && j > 0)
                            {
                                sb.Insert(0, "<" + orAndCondition + ">");
                                sb.Append("</" + orAndCondition + ">");
                            }
                            j++;

                        }
                        else if (parameters[i].Split(';')[2] == "Bt")
                        {
                            AppendBt(sb, parameters[i]);
                            if (i > 0 && j > 0)
                            {
                                sb.Insert(0, "<" + orAndCondition + ">");
                                sb.Append("</" + orAndCondition + ">");
                            }
                            j++;

                        }
                        else if (parameters[i].Split(';')[2] == "Ct")
                        {
                            AppendCt(sb, parameters[i], fname);
                            if (i > 0 && j > 0)
                            {
                                sb.Insert(0, "<" + orAndCondition + ">");
                                sb.Append("</" + orAndCondition + ">");
                            }
                            j++;

                        }
                        else if (parameters[i].Split(';')[2] == "Bg")
                        {
                            AppendBg(sb, parameters[i], fname);
                            if (i > 0 && j > 0)
                            {
                                sb.Insert(0, "<" + orAndCondition + ">");
                                sb.Append("</" + orAndCondition + ">");
                            }
                            j++;

                        }
                    }
                }
            }
            //if (isIncludeWhereClause)
            //{
            //    sb.Insert(0, "<Where><And><Eq><FieldRef Name='FileDirRef' /><Value Type='Text'>" + fname + "</Value></Eq>");
            //    // sb.Insert(0, "<Where>");
            //    sb.Append("</And></Where>");
            //}
            if (isIncludeWhereClause)
            {
                sb.Insert(0, "<Where><And><Eq><FieldRef Name='FileDirRef' /><Value Type='Text'>" + fname + "</Value></Eq>");
                // sb.Insert(0, "<Where>");
                sb.Append("</And></Where>");
            }

            return @"<View Scope='Recursive'><Query>" + sb.ToString() + "</Query></View>";
        }
        public static void AppendEQ(StringBuilder sb, string value)
        {
            string[] field = value.Split(';');
            if (field.Length > 1)
            {
                sb.AppendFormat(" <{0}>", field[2].ToString());
                sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                sb.AppendFormat("<Value Type='{0}'>{1}</Value>", field[1].ToString(), field[3].ToString());
                sb.AppendFormat("</{0}>", field[2].ToString());
            }
        }
        public static void AppendIn(StringBuilder sb, string value)
        {
            string[] field = value.Split(';');
            if (field.Length > 1)
            {
                sb.AppendFormat("<{0}>", field[2].ToString());
                sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                sb.AppendFormat("<Values>", field[0].ToString());
                foreach (var item in field[3].ToString().Split(','))
                {
                    sb.AppendFormat("<Value Type='{0}'>{1}</Value>", field[1].ToString(), item.ToString());
                }

                sb.AppendFormat("</Values>", field[0].ToString());


                sb.AppendFormat("</{0}>", field[2].ToString());
            }
        }

        public static void AppendCt(StringBuilder sb, string value, string fname)
        {

            //AccountId;Text;Ct;5PXKAR,5PXKAR,59XMBR

            string[] field = value.Split(';');
            string[] accIds = field[3].Split(',');
            int j = 0;

            if (accIds.Length == 1)
            {
                sb.AppendFormat("<And><Eq>");
                sb.AppendFormat("<FieldRef Name='FileDirRef'/>");
                sb.AppendFormat("<Value Type='Text'>{0}</Value>", fname);
                sb.AppendFormat("</Eq>");
                sb.AppendFormat("<Contains>");
                sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                sb.AppendFormat("<Value Type='Text'>{0}</Value>", accIds[j]);
                sb.AppendFormat("</Contains>");
                sb.Append("</And>");
            }
            if (accIds.Length == 2)
            {
                sb.AppendFormat("<Or>");
                sb.AppendFormat("<And><Eq>");
                sb.AppendFormat("<FieldRef Name='FileDirRef'/>");
                sb.AppendFormat("<Value Type='Text'>{0}</Value>", fname);
                sb.AppendFormat("</Eq>");
                sb.AppendFormat("<Contains>");
                sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                sb.AppendFormat("<Value Type='Text'>{0}</Value>", accIds[0]);
                sb.AppendFormat("</Contains>");
                sb.Append("</And>");
                sb.AppendFormat("<And><Eq>");
                sb.AppendFormat("<FieldRef Name='FileDirRef'/>");
                sb.AppendFormat("<Value Type='Text'>{0}</Value>", fname);
                sb.AppendFormat("</Eq>");
                sb.AppendFormat("<Contains>");
                sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                sb.AppendFormat("<Value Type='Text'>{0}</Value>", accIds[1]);
                sb.AppendFormat("</Contains>");
                sb.Append("</And></Or>");
            }

            if (accIds.Length > 2)
            {
                string closeOr = string.Empty;
                for (int i = 0; i < accIds.Length / 2 + 1; i++)
                {
                    j = i * 2;


                    if (accIds.Length > j)
                    {
                        sb.AppendFormat("<Or>");
                        sb.AppendFormat("<And><Eq>");
                        sb.AppendFormat("<FieldRef Name='FileDirRef'/>");
                        sb.AppendFormat("<Value Type='Text'>{0}</Value>", fname);
                        sb.AppendFormat("</Eq>");
                        sb.AppendFormat("<Contains>");
                        sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                        sb.AppendFormat("<Value Type='Text'>{0}</Value>", accIds[j]);
                        sb.AppendFormat("</Contains>");
                        sb.Append("</And>");

                        if (j == i * 2 && accIds.Length - 1 > j + 1)
                        {

                            sb.AppendFormat("<Or><And><Eq>");
                            sb.AppendFormat("<FieldRef Name='FileDirRef'/>");
                            sb.AppendFormat("<Value Type='Text'>{0}</Value>", fname);
                            sb.AppendFormat("</Eq>");
                            sb.AppendFormat("<Contains>");
                            sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                            sb.AppendFormat("<Value Type='Text'>{0}</Value>", accIds[j + 1]);
                            sb.AppendFormat("</Contains>");
                            sb.Append("</And>");
                            closeOr = closeOr + "</Or></Or>";
                        }
                        else if (accIds.Length - 1 == j + 1)
                        {
                            sb.AppendFormat("<And><Eq>");
                            sb.AppendFormat("<FieldRef Name='FileDirRef'/>");
                            sb.AppendFormat("<Value Type='Text'>{0}</Value>", fname);
                            sb.AppendFormat("</Eq>");
                            sb.AppendFormat("<Contains>");
                            sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                            sb.AppendFormat("<Value Type='Text'>{0}</Value>", accIds[j + 1]);
                            sb.AppendFormat("</Contains>");
                            sb.Append("</And>");
                            closeOr = closeOr + "</Or>";
                        }
                        else
                        {

                            sb.AppendFormat("<And><Eq>");
                            sb.AppendFormat("<FieldRef Name='FileDirRef'/>");
                            sb.AppendFormat("<Value Type='Text'>{0}</Value>", fname);
                            sb.AppendFormat("</Eq>");
                            sb.AppendFormat("<Contains>");
                            sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                            sb.AppendFormat("<Value Type='Text'>{0}</Value>", accIds[j]);
                            sb.AppendFormat("</Contains>");
                            sb.Append("</And>");
                            closeOr = closeOr + "</Or>";

                        }
                    }

                }
                sb.Append(closeOr);
            }


            //foreach(string s in accIds)
            //{
            //    sb.AppendFormat("<Or>");
            //    sb.AppendFormat("<Contains>");
            //    sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
            //    sb.AppendFormat("<Value Type='Text'>{0}</Value>", s);
            //    sb.AppendFormat("</Contains>");
            //    closeOr = closeOr + "</Or>";
            //}
            //sb.Append(closeOr);
        }

        public static void AppendBg(StringBuilder sb, string value, string fname)
        {

            //AccountId;Text;Ct;5PXKAR,5PXKAR,59XMBR

            string[] field = value.Split(';');
            string[] accIds = field[3].Split(',');
            int j = 0;

            if (accIds.Length == 1)
            {
                sb.AppendFormat("<And><Eq>");
                sb.AppendFormat("<FieldRef Name='FileDirRef'/>");
                sb.AppendFormat("<Value Type='Text'>{0}</Value>", fname);
                sb.AppendFormat("</Eq>");
                sb.AppendFormat("<BeginsWith>");
                sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                sb.AppendFormat("<Value Type='Text'>{0}</Value>", accIds[j]);
                sb.AppendFormat("</BeginsWith>");
                sb.Append("</And>");
            }
            if (accIds.Length == 2)
            {
                sb.AppendFormat("<Or>");
                sb.AppendFormat("<And><Eq>");
                sb.AppendFormat("<FieldRef Name='FileDirRef'/>");
                sb.AppendFormat("<Value Type='Text'>{0}</Value>", fname);
                sb.AppendFormat("</Eq>");
                sb.AppendFormat("<BeginsWith>");
                sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                sb.AppendFormat("<Value Type='Text'>{0}</Value>", accIds[0]);
                sb.AppendFormat("</BeginsWith>");
                sb.Append("</And>");
                sb.AppendFormat("<And><Eq>");
                sb.AppendFormat("<FieldRef Name='FileDirRef'/>");
                sb.AppendFormat("<Value Type='Text'>{0}</Value>", fname);
                sb.AppendFormat("</Eq>");
                sb.AppendFormat("<BeginsWith>");
                sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                sb.AppendFormat("<Value Type='Text'>{0}</Value>", accIds[1]);
                sb.AppendFormat("</BeginsWith>");
                sb.Append("</And></Or>");
            }

            if (accIds.Length > 2)
            {
                string closeOr = string.Empty;
                for (int i = 0; i < accIds.Length / 2 + 1; i++)
                {
                    j = i * 2;


                    if (accIds.Length > j)
                    {
                        sb.AppendFormat("<Or>");
                        sb.AppendFormat("<And><Eq>");
                        sb.AppendFormat("<FieldRef Name='FileDirRef'/>");
                        sb.AppendFormat("<Value Type='Text'>{0}</Value>", fname);
                        sb.AppendFormat("</Eq>");
                        sb.AppendFormat("<BeginsWith>");
                        sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                        sb.AppendFormat("<Value Type='Text'>{0}</Value>", accIds[j]);
                        sb.AppendFormat("</BeginsWith>");
                        sb.Append("</And>");

                        if (j == i * 2 && accIds.Length - 1 > j + 1)
                        {

                            sb.AppendFormat("<Or><And><Eq>");
                            sb.AppendFormat("<FieldRef Name='FileDirRef'/>");
                            sb.AppendFormat("<Value Type='Text'>{0}</Value>", fname);
                            sb.AppendFormat("</Eq>");
                            sb.AppendFormat("<BeginsWith>");
                            sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                            sb.AppendFormat("<Value Type='Text'>{0}</Value>", accIds[j + 1]);
                            sb.AppendFormat("</BeginsWith>");
                            sb.Append("</And>");
                            closeOr = closeOr + "</Or></Or>";
                        }
                        else if (accIds.Length - 1 == j + 1)
                        {
                            sb.AppendFormat("<And><Eq>");
                            sb.AppendFormat("<FieldRef Name='FileDirRef'/>");
                            sb.AppendFormat("<Value Type='Text'>{0}</Value>", fname);
                            sb.AppendFormat("</Eq>");
                            sb.AppendFormat("<BeginsWith>");
                            sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                            sb.AppendFormat("<Value Type='Text'>{0}</Value>", accIds[j + 1]);
                            sb.AppendFormat("</BeginsWith>");
                            sb.Append("</And>");
                            closeOr = closeOr + "</Or>";
                        }
                        else
                        {

                            sb.AppendFormat("<And><Eq>");
                            sb.AppendFormat("<FieldRef Name='FileDirRef'/>");
                            sb.AppendFormat("<Value Type='Text'>{0}</Value>", fname);
                            sb.AppendFormat("</Eq>");
                            sb.AppendFormat("<BeginsWith>");
                            sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                            sb.AppendFormat("<Value Type='Text'>{0}</Value>", accIds[j]);
                            sb.AppendFormat("</BeginsWith>");
                            sb.Append("</And>");
                            closeOr = closeOr + "</Or>";

                        }
                    }

                }
                sb.Append(closeOr);
            }
        }

        public static void AppendBt(StringBuilder sb, string value)
        {
            string[] field = value.Split(';');
            if (field.Length > 1)
            {
                sb.AppendFormat("<And><Geq>");
                sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                sb.AppendFormat("<Value  IncludeTimeValue=\"TRUE\" Type='{0}'>{1}</Value>", field[1].ToString(), field[3].ToString());
                sb.AppendFormat("</Geq>");
                sb.AppendFormat("<Leq>");
                sb.AppendFormat("<FieldRef Name='{0}'/>", field[0].ToString());
                sb.AppendFormat("<Value  IncludeTimeValue=\"TRUE\" Type='{0}'>{1}</Value>", field[1].ToString(), field[4].ToString());
                sb.AppendFormat("</Leq></And>");
            }


        }
    }
}
