﻿using Newtonsoft.Json;

namespace MicroSaasWebApi.Models.AlphaCCO
{  

    public class AlphaCCOQuestions
    {
        public int SurveyId { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public object InstructionsDocument { get; set; }
        public object Instructions { get; set; }
        public bool IsForExternalUser { get; set; }
        public string AssignedUser { get; set; }
        public int RegisterAnswerId { get; set; }
        public string SurveyStatus { get; set; }
        public int SurveyStatusId { get; set; }
        public string SurveyStatusMessage { get; set; }
        public object SurveyTopic { get; set; }
        public string StartDate { get; set; }
        public string DueDate { get; set; }
        public bool CanRevertSubmission { get; set; }
        public bool CanAnswerOnBehalf { get; set; }
        public bool CanAddAnswer { get; set; }
        public bool IsOnBehalf { get; set; }
        public bool CanGotoValidation { get; set; }
        public bool CanAddCorrectiveAction { get; set; }
        public int BatchTaskId { get; set; }
        public int NextTaskId { get; set; }
        public int PreviousTaskId { get; set; }
        public int CurrentTaskIndex { get; set; }
        public int TotalTaskCount { get; set; }
        public List<object> Risks { get; set; }
        public object Period { get; set; }
        public List<object> Tags { get; set; }
        public List<Page> Pages { get; set; }
        public List<Question> Questions { get; set; }
        public PageConditions PageConditions { get; set; }
        public QuestionConditions QuestionConditions { get; set; }
        public Answers Answers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }
    public class Page
    {
        public int Id { get; set; }
        public int Order { get; set; }
        public string Name { get; set; }
    }
    public class Question
    {
        public int Id { get; set; }
        public int RegisterAnswerItemId { get; set; }
        public object Answer { get; set; }
        public string Text { get; set; }
        public string Type { get; set; }
        public bool Required { get; set; }
        public string Tooltip { get; set; }
        public string Page { get; set; }
        public int PageOrder { get; set; }
        public bool Visible { get; set; }
        public bool Disabled { get; set; }
        public bool IsDefaulted { get; set; }
        public bool IsIncludedRequestEmail { get; set; }
        public int MinInputLength { get; set; }
        public int MaxInputLength { get; set; }
        public object NumberFormatString { get; set; }
        public int DecimalPlaces { get; set; }
        public List<Choice> Choices { get; set; }
        public object DateRestriction { get; set; }
        public bool IsMatrix { get; set; }
        public object Matrix { get; set; }
        public object Conditions { get; set; }
    }


    public class PageConditions
    {
        //[JsonProperty("1395", NullValueHandling = NullValueHandling.Ignore)]
        //public _1395 _1395 { get; set; }

        //[JsonProperty("1394", NullValueHandling = NullValueHandling.Ignore)]
        //public _1394 _1394 { get; set; }

        //[JsonProperty("1406", NullValueHandling = NullValueHandling.Ignore)]
        //public _1406 _1406 { get; set; }

        //[JsonProperty("1407", NullValueHandling = NullValueHandling.Ignore)]
        //public _1407 _1407 { get; set; }
    }

    
    public class QuestionConditions
    {
        [JsonProperty("30077", NullValueHandling = NullValueHandling.Ignore)]
        public _30077 _30077 { get; set; }

        [JsonProperty("30078", NullValueHandling = NullValueHandling.Ignore)]
        public _30078 _30078 { get; set; }

        [JsonProperty("30079", NullValueHandling = NullValueHandling.Ignore)]
        public _30079 _30079 { get; set; }

        [JsonProperty("30080", NullValueHandling = NullValueHandling.Ignore)]
        public _30080 _30080 { get; set; }

        [JsonProperty("30082", NullValueHandling = NullValueHandling.Ignore)]
        public _30082 _30082 { get; set; }

        [JsonProperty("30083", NullValueHandling = NullValueHandling.Ignore)]
        public _30083 _30083 { get; set; }

        [JsonProperty("30084", NullValueHandling = NullValueHandling.Ignore)]
        public _30084 _30084 { get; set; }

        [JsonProperty("30085", NullValueHandling = NullValueHandling.Ignore)]
        public _30085 _30085 { get; set; }

        [JsonProperty("30086", NullValueHandling = NullValueHandling.Ignore)]
        public _30086 _30086 { get; set; }

        [JsonProperty("30087", NullValueHandling = NullValueHandling.Ignore)]
        public _30087 _30087 { get; set; }

        [JsonProperty("30088", NullValueHandling = NullValueHandling.Ignore)]
        public _30088 _30088 { get; set; }

        [JsonProperty("30089", NullValueHandling = NullValueHandling.Ignore)]
        public _30089 _30089 { get; set; }

        [JsonProperty("30090", NullValueHandling = NullValueHandling.Ignore)]
        public _30090 _30090 { get; set; }

        [JsonProperty("30091", NullValueHandling = NullValueHandling.Ignore)]
        public _30091 _30091 { get; set; }

        [JsonProperty("30092", NullValueHandling = NullValueHandling.Ignore)]
        public _30092 _30092 { get; set; }

        [JsonProperty("30093", NullValueHandling = NullValueHandling.Ignore)]
        public _30093 _30093 { get; set; }

        [JsonProperty("30094", NullValueHandling = NullValueHandling.Ignore)]
        public _30094 _30094 { get; set; }

        [JsonProperty("30103", NullValueHandling = NullValueHandling.Ignore)]
        public _30103 _30103 { get; set; }

        [JsonProperty("30107", NullValueHandling = NullValueHandling.Ignore)]
        public _30107 _30107 { get; set; }

        [JsonProperty("30108", NullValueHandling = NullValueHandling.Ignore)]
        public _30108 _30108 { get; set; }

        [JsonProperty("30109", NullValueHandling = NullValueHandling.Ignore)]
        public _30109 _30109 { get; set; }

        [JsonProperty("30110", NullValueHandling = NullValueHandling.Ignore)]
        public _30110 _30110 { get; set; }

        [JsonProperty("30111", NullValueHandling = NullValueHandling.Ignore)]
        public _30111 _30111 { get; set; }

        [JsonProperty("30112", NullValueHandling = NullValueHandling.Ignore)]
        public _30112 _30112 { get; set; }

        [JsonProperty("30131", NullValueHandling = NullValueHandling.Ignore)]
        public _30131 _30131 { get; set; }

        [JsonProperty("30132", NullValueHandling = NullValueHandling.Ignore)]
        public _30132 _30132 { get; set; }

        [JsonProperty("30133", NullValueHandling = NullValueHandling.Ignore)]
        public _30133 _30133 { get; set; }

        [JsonProperty("30134", NullValueHandling = NullValueHandling.Ignore)]
        public _30134 _30134 { get; set; }

        [JsonProperty("30135", NullValueHandling = NullValueHandling.Ignore)]
        public _30135 _30135 { get; set; }

        [JsonProperty("30327", NullValueHandling = NullValueHandling.Ignore)]
        public _30327 _30327 { get; set; }

        [JsonProperty("30326", NullValueHandling = NullValueHandling.Ignore)]
        public _30326 _30326 { get; set; }

        [JsonProperty("30328", NullValueHandling = NullValueHandling.Ignore)]
        public _30328 _30328 { get; set; }

        [JsonProperty("30329", NullValueHandling = NullValueHandling.Ignore)]
        public _30329 _30329 { get; set; }

        [JsonProperty("30331", NullValueHandling = NullValueHandling.Ignore)]
        public _30331 _30331 { get; set; }

        [JsonProperty("30731", NullValueHandling = NullValueHandling.Ignore)]
        public _30731 _30731 { get; set; }

        [JsonProperty("30353", NullValueHandling = NullValueHandling.Ignore)]
        public _30353 _30353 { get; set; }

        [JsonProperty("30354", NullValueHandling = NullValueHandling.Ignore)]
        public _30354 _30354 { get; set; }

        [JsonProperty("30358", NullValueHandling = NullValueHandling.Ignore)]
        public _30358 _30358 { get; set; }

        [JsonProperty("30366", NullValueHandling = NullValueHandling.Ignore)]
        public _30366 _30366 { get; set; }

        [JsonProperty("30367", NullValueHandling = NullValueHandling.Ignore)]
        public _30367 _30367 { get; set; }

        [JsonProperty("30368", NullValueHandling = NullValueHandling.Ignore)]
        public _30368 _30368 { get; set; }

        [JsonProperty("30369", NullValueHandling = NullValueHandling.Ignore)]
        public _30369 _30369 { get; set; }

        [JsonProperty("30370", NullValueHandling = NullValueHandling.Ignore)]
        public _30370 _30370 { get; set; }

        [JsonProperty("30372", NullValueHandling = NullValueHandling.Ignore)]
        public _30372 _30372 { get; set; }

        [JsonProperty("30374", NullValueHandling = NullValueHandling.Ignore)]
        public _30374 _30374 { get; set; }

        [JsonProperty("30397", NullValueHandling = NullValueHandling.Ignore)]
        public _30397 _30397 { get; set; }

        [JsonProperty("30398", NullValueHandling = NullValueHandling.Ignore)]
        public _30398 _30398 { get; set; }

        [JsonProperty("30399", NullValueHandling = NullValueHandling.Ignore)]
        public _30399 _30399 { get; set; }

        [JsonProperty("30400", NullValueHandling = NullValueHandling.Ignore)]
        public _30400 _30400 { get; set; }

        [JsonProperty("30694", NullValueHandling = NullValueHandling.Ignore)]
        public _30694 _30694 { get; set; }

        [JsonProperty("30693", NullValueHandling = NullValueHandling.Ignore)]
        public _30693 _30693 { get; set; }

        [JsonProperty("30692", NullValueHandling = NullValueHandling.Ignore)]
        public _30692 _30692 { get; set; }

        [JsonProperty("30691", NullValueHandling = NullValueHandling.Ignore)]
        public _30691 _30691 { get; set; }

        [JsonProperty("30690", NullValueHandling = NullValueHandling.Ignore)]
        public _30690 _30690 { get; set; }

        [JsonProperty("30689", NullValueHandling = NullValueHandling.Ignore)]
        public _30689 _30689 { get; set; }

        [JsonProperty("30688", NullValueHandling = NullValueHandling.Ignore)]
        public _30688 _30688 { get; set; }

        [JsonProperty("30687", NullValueHandling = NullValueHandling.Ignore)]
        public _30687 _30687 { get; set; }

        [JsonProperty("30686", NullValueHandling = NullValueHandling.Ignore)]
        public _30686 _30686 { get; set; }

        [JsonProperty("30708", NullValueHandling = NullValueHandling.Ignore)]
        public _30708 _30708 { get; set; }

        [JsonProperty("30709", NullValueHandling = NullValueHandling.Ignore)]
        public _30709 _30709 { get; set; }

        [JsonProperty("30722", NullValueHandling = NullValueHandling.Ignore)]
        public _30722 _30722 { get; set; }

        [JsonProperty("30723", NullValueHandling = NullValueHandling.Ignore)]
        public _30723 _30723 { get; set; }

        [JsonProperty("30724", NullValueHandling = NullValueHandling.Ignore)]
        public _30724 _30724 { get; set; }

        [JsonProperty("30732", NullValueHandling = NullValueHandling.Ignore)]
        public _30732 _30732 { get; set; }

        [JsonProperty("30740", NullValueHandling = NullValueHandling.Ignore)]
        public _30740 _30740 { get; set; }

        [JsonProperty("30743", NullValueHandling = NullValueHandling.Ignore)]
        public _30743 _30743 { get; set; }

        [JsonProperty("30744", NullValueHandling = NullValueHandling.Ignore)]
        public _30744 _30744 { get; set; }

        [JsonProperty("30745", NullValueHandling = NullValueHandling.Ignore)]
        public _30745 _30745 { get; set; }

        [JsonProperty("30749", NullValueHandling = NullValueHandling.Ignore)]
        public _30749 _30749 { get; set; }

        [JsonProperty("30750", NullValueHandling = NullValueHandling.Ignore)]
        public _30750 _30750 { get; set; }

        [JsonProperty("30751", NullValueHandling = NullValueHandling.Ignore)]
        public _30751 _30751 { get; set; }

        [JsonProperty("30752", NullValueHandling = NullValueHandling.Ignore)]
        public _30752 _30752 { get; set; }

        [JsonProperty("30753", NullValueHandling = NullValueHandling.Ignore)]
        public _30753 _30753 { get; set; }

        [JsonProperty("30754", NullValueHandling = NullValueHandling.Ignore)]
        public _30754 _30754 { get; set; }

        [JsonProperty("30755", NullValueHandling = NullValueHandling.Ignore)]
        public _30755 _30755 { get; set; }

        [JsonProperty("30756", NullValueHandling = NullValueHandling.Ignore)]
        public _30756 _30756 { get; set; }

        [JsonProperty("30758", NullValueHandling = NullValueHandling.Ignore)]
        public _30758 _30758 { get; set; }

        [JsonProperty("30759", NullValueHandling = NullValueHandling.Ignore)]
        public _30759 _30759 { get; set; }

        [JsonProperty("30760", NullValueHandling = NullValueHandling.Ignore)]
        public _30760 _30760 { get; set; }

        [JsonProperty("30761", NullValueHandling = NullValueHandling.Ignore)]
        public _30761 _30761 { get; set; }

        [JsonProperty("30762", NullValueHandling = NullValueHandling.Ignore)]
        public _30762 _30762 { get; set; }

        [JsonProperty("30763", NullValueHandling = NullValueHandling.Ignore)]
        public _30763 _30763 { get; set; }

        [JsonProperty("30764", NullValueHandling = NullValueHandling.Ignore)]
        public _30764 _30764 { get; set; }

        [JsonProperty("30765", NullValueHandling = NullValueHandling.Ignore)]
        public _30765 _30765 { get; set; }

        [JsonProperty("30766", NullValueHandling = NullValueHandling.Ignore)]
        public _30766 _30766 { get; set; }

        [JsonProperty("30777", NullValueHandling = NullValueHandling.Ignore)]
        public _30777 _30777 { get; set; }

        [JsonProperty("30788", NullValueHandling = NullValueHandling.Ignore)]
        public _30788 _30788 { get; set; }

        [JsonProperty("30757", NullValueHandling = NullValueHandling.Ignore)]
        public _30757 _30757 { get; set; }

        [JsonProperty("30768", NullValueHandling = NullValueHandling.Ignore)]
        public _30768 _30768 { get; set; }

        [JsonProperty("30769", NullValueHandling = NullValueHandling.Ignore)]
        public _30769 _30769 { get; set; }

        [JsonProperty("30770", NullValueHandling = NullValueHandling.Ignore)]
        public _30770 _30770 { get; set; }

        [JsonProperty("30786", NullValueHandling = NullValueHandling.Ignore)]
        public _30786 _30786 { get; set; }

        [JsonProperty("30783", NullValueHandling = NullValueHandling.Ignore)]
        public _30783 _30783 { get; set; }

        [JsonProperty("30717", NullValueHandling = NullValueHandling.Ignore)]
        public _30717 _30717 { get; set; }

        [JsonProperty("30718", NullValueHandling = NullValueHandling.Ignore)]
        public _30718 _30718 { get; set; }

        [JsonProperty("30719", NullValueHandling = NullValueHandling.Ignore)]
        public _30719 _30719 { get; set; }

        [JsonProperty("30785", NullValueHandling = NullValueHandling.Ignore)]
        public _30785 _30785 { get; set; }

        [JsonProperty("30728", NullValueHandling = NullValueHandling.Ignore)]
        public _30728 _30728 { get; set; }

        [JsonProperty("30729", NullValueHandling = NullValueHandling.Ignore)]
        public _30729 _30729 { get; set; }

        [JsonProperty("30730", NullValueHandling = NullValueHandling.Ignore)]
        public _30730 _30730 { get; set; }
    }






    public class Values
    {
        [JsonProperty("30323", NullValueHandling = NullValueHandling.Ignore)]
        public _30323 _30323 { get; set; }

        [JsonProperty("30326", NullValueHandling = NullValueHandling.Ignore)]
        public _30326 _30326 { get; set; }

        [JsonProperty("30328", NullValueHandling = NullValueHandling.Ignore)]
        public _30328 _30328 { get; set; }

        [JsonProperty("30777", NullValueHandling = NullValueHandling.Ignore)]
        public _30777 _30777 { get; set; }

        [JsonProperty("30329", NullValueHandling = NullValueHandling.Ignore)]
        public _30329 _30329 { get; set; }

        [JsonProperty("30327", NullValueHandling = NullValueHandling.Ignore)]
        public _30327 _30327 { get; set; }

        [JsonProperty("30781", NullValueHandling = NullValueHandling.Ignore)]
        public _30781 _30781 { get; set; }

        [JsonProperty("30789", NullValueHandling = NullValueHandling.Ignore)]
        public _30789 _30789 { get; set; }

        [JsonProperty("30360", NullValueHandling = NullValueHandling.Ignore)]
        public _30360 _30360 { get; set; }

        [JsonProperty("30362", NullValueHandling = NullValueHandling.Ignore)]
        public _30362 _30362 { get; set; }

        [JsonProperty("30771", NullValueHandling = NullValueHandling.Ignore)]
        public _30771 _30771 { get; set; }

        [JsonProperty("30772", NullValueHandling = NullValueHandling.Ignore)]
        public _30772 _30772 { get; set; }

        [JsonProperty("30773", NullValueHandling = NullValueHandling.Ignore)]
        public _30773 _30773 { get; set; }

        [JsonProperty("30774", NullValueHandling = NullValueHandling.Ignore)]
        public _30774 _30774 { get; set; }

        [JsonProperty("30775", NullValueHandling = NullValueHandling.Ignore)]
        public _30775 _30775 { get; set; }

        [JsonProperty("30061", NullValueHandling = NullValueHandling.Ignore)]
        public _30061 _30061 { get; set; }

        [JsonProperty("30096", NullValueHandling = NullValueHandling.Ignore)]
        public _30096 _30096 { get; set; }

        [JsonProperty("30058", NullValueHandling = NullValueHandling.Ignore)]
        public _30058 _30058 { get; set; }

        [JsonProperty("30060", NullValueHandling = NullValueHandling.Ignore)]
        public _30060 _30060 { get; set; }

        [JsonProperty("30055", NullValueHandling = NullValueHandling.Ignore)]
        public _30055 _30055 { get; set; }

        [JsonProperty("30056", NullValueHandling = NullValueHandling.Ignore)]
        public _30056 _30056 { get; set; }

        [JsonProperty("30057", NullValueHandling = NullValueHandling.Ignore)]
        public _30057 _30057 { get; set; }

        [JsonProperty("30059", NullValueHandling = NullValueHandling.Ignore)]
        public _30059 _30059 { get; set; }

        [JsonProperty("30063", NullValueHandling = NullValueHandling.Ignore)]
        public _30063 _30063 { get; set; }

        [JsonProperty("30064", NullValueHandling = NullValueHandling.Ignore)]
        public _30064 _30064 { get; set; }

        [JsonProperty("30062", NullValueHandling = NullValueHandling.Ignore)]
        public _30062 _30062 { get; set; }

        [JsonProperty("30065", NullValueHandling = NullValueHandling.Ignore)]
        public _30065 _30065 { get; set; }

        [JsonProperty("30066", NullValueHandling = NullValueHandling.Ignore)]
        public _30066 _30066 { get; set; }

        [JsonProperty("30068", NullValueHandling = NullValueHandling.Ignore)]
        public _30068 _30068 { get; set; }

        [JsonProperty("30069", NullValueHandling = NullValueHandling.Ignore)]
        public _30069 _30069 { get; set; }

        [JsonProperty("30070", NullValueHandling = NullValueHandling.Ignore)]
        public _30070 _30070 { get; set; }

        [JsonProperty("30071", NullValueHandling = NullValueHandling.Ignore)]
        public _30071 _30071 { get; set; }

        [JsonProperty("30072", NullValueHandling = NullValueHandling.Ignore)]
        public _30072 _30072 { get; set; }

        [JsonProperty("30073", NullValueHandling = NullValueHandling.Ignore)]
        public _30073 _30073 { get; set; }

        [JsonProperty("30074", NullValueHandling = NullValueHandling.Ignore)]
        public _30074 _30074 { get; set; }

        [JsonProperty("30075", NullValueHandling = NullValueHandling.Ignore)]
        public _30075 _30075 { get; set; }

        [JsonProperty("30076", NullValueHandling = NullValueHandling.Ignore)]
        public _30076 _30076 { get; set; }

        [JsonProperty("30077", NullValueHandling = NullValueHandling.Ignore)]
        public _30077 _30077 { get; set; }

        [JsonProperty("30078", NullValueHandling = NullValueHandling.Ignore)]
        public _30078 _30078 { get; set; }

        [JsonProperty("30079", NullValueHandling = NullValueHandling.Ignore)]
        public _30079 _30079 { get; set; }

        [JsonProperty("30080", NullValueHandling = NullValueHandling.Ignore)]
        public _30080 _30080 { get; set; }

        [JsonProperty("30081", NullValueHandling = NullValueHandling.Ignore)]
        public _30081 _30081 { get; set; }

        [JsonProperty("30082", NullValueHandling = NullValueHandling.Ignore)]
        public _30082 _30082 { get; set; }

        [JsonProperty("30083", NullValueHandling = NullValueHandling.Ignore)]
        public _30083 _30083 { get; set; }

        [JsonProperty("30084", NullValueHandling = NullValueHandling.Ignore)]
        public _30084 _30084 { get; set; }

        [JsonProperty("30085", NullValueHandling = NullValueHandling.Ignore)]
        public _30085 _30085 { get; set; }

        [JsonProperty("30086", NullValueHandling = NullValueHandling.Ignore)]
        public _30086 _30086 { get; set; }

        [JsonProperty("30087", NullValueHandling = NullValueHandling.Ignore)]
        public _30087 _30087 { get; set; }

        [JsonProperty("30088", NullValueHandling = NullValueHandling.Ignore)]
        public _30088 _30088 { get; set; }

        [JsonProperty("30089", NullValueHandling = NullValueHandling.Ignore)]
        public _30089 _30089 { get; set; }

        [JsonProperty("30090", NullValueHandling = NullValueHandling.Ignore)]
        public _30090 _30090 { get; set; }

        [JsonProperty("30091", NullValueHandling = NullValueHandling.Ignore)]
        public _30091 _30091 { get; set; }

        [JsonProperty("30092", NullValueHandling = NullValueHandling.Ignore)]
        public _30092 _30092 { get; set; }

        [JsonProperty("30093", NullValueHandling = NullValueHandling.Ignore)]
        public _30093 _30093 { get; set; }

        [JsonProperty("30094", NullValueHandling = NullValueHandling.Ignore)]
        public _30094 _30094 { get; set; }

        [JsonProperty("30095", NullValueHandling = NullValueHandling.Ignore)]
        public _30095 _30095 { get; set; }

        [JsonProperty("30097", NullValueHandling = NullValueHandling.Ignore)]
        public _30097 _30097 { get; set; }

        [JsonProperty("30098", NullValueHandling = NullValueHandling.Ignore)]
        public _30098 _30098 { get; set; }

        [JsonProperty("30099", NullValueHandling = NullValueHandling.Ignore)]
        public _30099 _30099 { get; set; }

        [JsonProperty("30100", NullValueHandling = NullValueHandling.Ignore)]
        public _30100 _30100 { get; set; }

        [JsonProperty("30103", NullValueHandling = NullValueHandling.Ignore)]
        public _30103 _30103 { get; set; }

        [JsonProperty("30105", NullValueHandling = NullValueHandling.Ignore)]
        public _30105 _30105 { get; set; }

        [JsonProperty("30106", NullValueHandling = NullValueHandling.Ignore)]
        public _30106 _30106 { get; set; }

        [JsonProperty("30107", NullValueHandling = NullValueHandling.Ignore)]
        public _30107 _30107 { get; set; }

        [JsonProperty("30108", NullValueHandling = NullValueHandling.Ignore)]
        public _30108 _30108 { get; set; }

        [JsonProperty("30109", NullValueHandling = NullValueHandling.Ignore)]
        public _30109 _30109 { get; set; }

        [JsonProperty("30110", NullValueHandling = NullValueHandling.Ignore)]
        public _30110 _30110 { get; set; }

        [JsonProperty("30111", NullValueHandling = NullValueHandling.Ignore)]
        public _30111 _30111 { get; set; }

        [JsonProperty("30112", NullValueHandling = NullValueHandling.Ignore)]
        public _30112 _30112 { get; set; }

        [JsonProperty("30114", NullValueHandling = NullValueHandling.Ignore)]
        public _30114 _30114 { get; set; }

        [JsonProperty("30118", NullValueHandling = NullValueHandling.Ignore)]
        public _30118 _30118 { get; set; }

        [JsonProperty("30119", NullValueHandling = NullValueHandling.Ignore)]
        public _30119 _30119 { get; set; }

        [JsonProperty("30120", NullValueHandling = NullValueHandling.Ignore)]
        public _30120 _30120 { get; set; }

        [JsonProperty("30121", NullValueHandling = NullValueHandling.Ignore)]
        public _30121 _30121 { get; set; }

        [JsonProperty("30123", NullValueHandling = NullValueHandling.Ignore)]
        public _30123 _30123 { get; set; }

        [JsonProperty("30124", NullValueHandling = NullValueHandling.Ignore)]
        public _30124 _30124 { get; set; }

        [JsonProperty("30125", NullValueHandling = NullValueHandling.Ignore)]
        public _30125 _30125 { get; set; }

        [JsonProperty("30126", NullValueHandling = NullValueHandling.Ignore)]
        public _30126 _30126 { get; set; }

        [JsonProperty("30127", NullValueHandling = NullValueHandling.Ignore)]
        public _30127 _30127 { get; set; }

        [JsonProperty("30128", NullValueHandling = NullValueHandling.Ignore)]
        public _30128 _30128 { get; set; }

        [JsonProperty("30130", NullValueHandling = NullValueHandling.Ignore)]
        public _30130 _30130 { get; set; }

        [JsonProperty("30131", NullValueHandling = NullValueHandling.Ignore)]
        public _30131 _30131 { get; set; }

        [JsonProperty("30132", NullValueHandling = NullValueHandling.Ignore)]
        public _30132 _30132 { get; set; }

        [JsonProperty("30133", NullValueHandling = NullValueHandling.Ignore)]
        public _30133 _30133 { get; set; }

        [JsonProperty("30134", NullValueHandling = NullValueHandling.Ignore)]
        public _30134 _30134 { get; set; }

        [JsonProperty("30135", NullValueHandling = NullValueHandling.Ignore)]
        public _30135 _30135 { get; set; }

        [JsonProperty("30138", NullValueHandling = NullValueHandling.Ignore)]
        public _30138 _30138 { get; set; }

        [JsonProperty("30790", NullValueHandling = NullValueHandling.Ignore)]
        public _30790 _30790 { get; set; }

        [JsonProperty("30344", NullValueHandling = NullValueHandling.Ignore)]
        public _30344 _30344 { get; set; }

        [JsonProperty("30324", NullValueHandling = NullValueHandling.Ignore)]
        public _30324 _30324 { get; set; }

        [JsonProperty("30330", NullValueHandling = NullValueHandling.Ignore)]
        public _30330 _30330 { get; set; }

        [JsonProperty("30331", NullValueHandling = NullValueHandling.Ignore)]
        public _30331 _30331 { get; set; }

        [JsonProperty("30395", NullValueHandling = NullValueHandling.Ignore)]
        public _30395 _30395 { get; set; }

        [JsonProperty("30339", NullValueHandling = NullValueHandling.Ignore)]
        public _30339 _30339 { get; set; }

        [JsonProperty("30337", NullValueHandling = NullValueHandling.Ignore)]
        public _30337 _30337 { get; set; }

        [JsonProperty("30685", NullValueHandling = NullValueHandling.Ignore)]
        public _30685 _30685 { get; set; }

        [JsonProperty("30342", NullValueHandling = NullValueHandling.Ignore)]
        public _30342 _30342 { get; set; }

        [JsonProperty("30325", NullValueHandling = NullValueHandling.Ignore)]
        public _30325 _30325 { get; set; }

        [JsonProperty("30332", NullValueHandling = NullValueHandling.Ignore)]
        public _30332 _30332 { get; set; }

        [JsonProperty("30333", NullValueHandling = NullValueHandling.Ignore)]
        public _30333 _30333 { get; set; }

        [JsonProperty("30334", NullValueHandling = NullValueHandling.Ignore)]
        public _30334 _30334 { get; set; }

        [JsonProperty("30361", NullValueHandling = NullValueHandling.Ignore)]
        public _30361 _30361 { get; set; }

        [JsonProperty("30335", NullValueHandling = NullValueHandling.Ignore)]
        public _30335 _30335 { get; set; }

        [JsonProperty("30396", NullValueHandling = NullValueHandling.Ignore)]
        public _30396 _30396 { get; set; }

        [JsonProperty("30397", NullValueHandling = NullValueHandling.Ignore)]
        public _30397 _30397 { get; set; }

        [JsonProperty("30398", NullValueHandling = NullValueHandling.Ignore)]
        public _30398 _30398 { get; set; }

        [JsonProperty("30399", NullValueHandling = NullValueHandling.Ignore)]
        public _30399 _30399 { get; set; }

        [JsonProperty("30400", NullValueHandling = NullValueHandling.Ignore)]
        public _30400 _30400 { get; set; }

        [JsonProperty("30695", NullValueHandling = NullValueHandling.Ignore)]
        public _30695 _30695 { get; set; }

        [JsonProperty("30694", NullValueHandling = NullValueHandling.Ignore)]
        public _30694 _30694 { get; set; }

        [JsonProperty("30693", NullValueHandling = NullValueHandling.Ignore)]
        public _30693 _30693 { get; set; }

        [JsonProperty("30692", NullValueHandling = NullValueHandling.Ignore)]
        public _30692 _30692 { get; set; }

        [JsonProperty("30691", NullValueHandling = NullValueHandling.Ignore)]
        public _30691 _30691 { get; set; }

        [JsonProperty("30690", NullValueHandling = NullValueHandling.Ignore)]
        public _30690 _30690 { get; set; }

        [JsonProperty("30689", NullValueHandling = NullValueHandling.Ignore)]
        public _30689 _30689 { get; set; }

        [JsonProperty("30688", NullValueHandling = NullValueHandling.Ignore)]
        public _30688 _30688 { get; set; }

        [JsonProperty("30687", NullValueHandling = NullValueHandling.Ignore)]
        public _30687 _30687 { get; set; }

        [JsonProperty("30686", NullValueHandling = NullValueHandling.Ignore)]
        public _30686 _30686 { get; set; }

        [JsonProperty("30343", NullValueHandling = NullValueHandling.Ignore)]
        public _30343 _30343 { get; set; }

        [JsonProperty("30402", NullValueHandling = NullValueHandling.Ignore)]
        public _30402 _30402 { get; set; }

        [JsonProperty("30696", NullValueHandling = NullValueHandling.Ignore)]
        public _30696 _30696 { get; set; }

        [JsonProperty("30345", NullValueHandling = NullValueHandling.Ignore)]
        public _30345 _30345 { get; set; }

        [JsonProperty("30346", NullValueHandling = NullValueHandling.Ignore)]
        public _30346 _30346 { get; set; }

        [JsonProperty("30347", NullValueHandling = NullValueHandling.Ignore)]
        public _30347 _30347 { get; set; }

        [JsonProperty("30348", NullValueHandling = NullValueHandling.Ignore)]
        public _30348 _30348 { get; set; }

        [JsonProperty("30349", NullValueHandling = NullValueHandling.Ignore)]
        public _30349 _30349 { get; set; }

        [JsonProperty("30697", NullValueHandling = NullValueHandling.Ignore)]
        public _30697 _30697 { get; set; }

        [JsonProperty("30712", NullValueHandling = NullValueHandling.Ignore)]
        public _30712 _30712 { get; set; }

        [JsonProperty("30354", NullValueHandling = NullValueHandling.Ignore)]
        public _30354 _30354 { get; set; }

        [JsonProperty("30358", NullValueHandling = NullValueHandling.Ignore)]
        public _30358 _30358 { get; set; }

        [JsonProperty("30713", NullValueHandling = NullValueHandling.Ignore)]
        public _30713 _30713 { get; set; }

        [JsonProperty("30714", NullValueHandling = NullValueHandling.Ignore)]
        public _30714 _30714 { get; set; }

        [JsonProperty("30725", NullValueHandling = NullValueHandling.Ignore)]
        public _30725 _30725 { get; set; }

        [JsonProperty("30787", NullValueHandling = NullValueHandling.Ignore)]
        public _30787 _30787 { get; set; }

        [JsonProperty("30783", NullValueHandling = NullValueHandling.Ignore)]
        public _30783 _30783 { get; set; }

        [JsonProperty("30717", NullValueHandling = NullValueHandling.Ignore)]
        public _30717 _30717 { get; set; }

        [JsonProperty("30718", NullValueHandling = NullValueHandling.Ignore)]
        public _30718 _30718 { get; set; }

        [JsonProperty("30719", NullValueHandling = NullValueHandling.Ignore)]
        public _30719 _30719 { get; set; }

        [JsonProperty("30720", NullValueHandling = NullValueHandling.Ignore)]
        public _30720 _30720 { get; set; }

        [JsonProperty("30722", NullValueHandling = NullValueHandling.Ignore)]
        public _30722 _30722 { get; set; }

        [JsonProperty("30724", NullValueHandling = NullValueHandling.Ignore)]
        public _30724 _30724 { get; set; }

        [JsonProperty("30723", NullValueHandling = NullValueHandling.Ignore)]
        public _30723 _30723 { get; set; }

        [JsonProperty("30788", NullValueHandling = NullValueHandling.Ignore)]
        public _30788 _30788 { get; set; }

        [JsonProperty("30785", NullValueHandling = NullValueHandling.Ignore)]
        public _30785 _30785 { get; set; }

        [JsonProperty("30728", NullValueHandling = NullValueHandling.Ignore)]
        public _30728 _30728 { get; set; }

        [JsonProperty("30729", NullValueHandling = NullValueHandling.Ignore)]
        public _30729 _30729 { get; set; }

        [JsonProperty("30730", NullValueHandling = NullValueHandling.Ignore)]
        public _30730 _30730 { get; set; }

        [JsonProperty("30698", NullValueHandling = NullValueHandling.Ignore)]
        public _30698 _30698 { get; set; }

        [JsonProperty("30699", NullValueHandling = NullValueHandling.Ignore)]
        public _30699 _30699 { get; set; }

        [JsonProperty("30700", NullValueHandling = NullValueHandling.Ignore)]
        public _30700 _30700 { get; set; }

        [JsonProperty("30701", NullValueHandling = NullValueHandling.Ignore)]
        public _30701 _30701 { get; set; }

        [JsonProperty("30702", NullValueHandling = NullValueHandling.Ignore)]
        public _30702 _30702 { get; set; }

        [JsonProperty("30703", NullValueHandling = NullValueHandling.Ignore)]
        public _30703 _30703 { get; set; }

        [JsonProperty("30704", NullValueHandling = NullValueHandling.Ignore)]
        public _30704 _30704 { get; set; }

        [JsonProperty("30705", NullValueHandling = NullValueHandling.Ignore)]
        public _30705 _30705 { get; set; }

        [JsonProperty("30706", NullValueHandling = NullValueHandling.Ignore)]
        public _30706 _30706 { get; set; }

        [JsonProperty("30707", NullValueHandling = NullValueHandling.Ignore)]
        public _30707 _30707 { get; set; }

        [JsonProperty("30708", NullValueHandling = NullValueHandling.Ignore)]
        public _30708 _30708 { get; set; }

        [JsonProperty("30709", NullValueHandling = NullValueHandling.Ignore)]
        public _30709 _30709 { get; set; }

        [JsonProperty("30351", NullValueHandling = NullValueHandling.Ignore)]
        public _30351 _30351 { get; set; }

        [JsonProperty("30352", NullValueHandling = NullValueHandling.Ignore)]
        public _30352 _30352 { get; set; }

        [JsonProperty("30353", NullValueHandling = NullValueHandling.Ignore)]
        public _30353 _30353 { get; set; }

        [JsonProperty("30731", NullValueHandling = NullValueHandling.Ignore)]
        public _30731 _30731 { get; set; }

        [JsonProperty("30732", NullValueHandling = NullValueHandling.Ignore)]
        public _30732 _30732 { get; set; }

        [JsonProperty("30733", NullValueHandling = NullValueHandling.Ignore)]
        public _30733 _30733 { get; set; }

        [JsonProperty("30734", NullValueHandling = NullValueHandling.Ignore)]
        public _30734 _30734 { get; set; }

        [JsonProperty("30735", NullValueHandling = NullValueHandling.Ignore)]
        public _30735 _30735 { get; set; }

        [JsonProperty("30736", NullValueHandling = NullValueHandling.Ignore)]
        public _30736 _30736 { get; set; }

        [JsonProperty("30737", NullValueHandling = NullValueHandling.Ignore)]
        public _30737 _30737 { get; set; }

        [JsonProperty("30738", NullValueHandling = NullValueHandling.Ignore)]
        public _30738 _30738 { get; set; }

        [JsonProperty("30739", NullValueHandling = NullValueHandling.Ignore)]
        public _30739 _30739 { get; set; }

        [JsonProperty("30740", NullValueHandling = NullValueHandling.Ignore)]
        public _30740 _30740 { get; set; }

        [JsonProperty("30743", NullValueHandling = NullValueHandling.Ignore)]
        public _30743 _30743 { get; set; }

        [JsonProperty("30744", NullValueHandling = NullValueHandling.Ignore)]
        public _30744 _30744 { get; set; }

        [JsonProperty("30745", NullValueHandling = NullValueHandling.Ignore)]
        public _30745 _30745 { get; set; }

        [JsonProperty("30742", NullValueHandling = NullValueHandling.Ignore)]
        public _30742 _30742 { get; set; }

        [JsonProperty("30746", NullValueHandling = NullValueHandling.Ignore)]
        public _30746 _30746 { get; set; }

        [JsonProperty("30747", NullValueHandling = NullValueHandling.Ignore)]
        public _30747 _30747 { get; set; }

        [JsonProperty("30748", NullValueHandling = NullValueHandling.Ignore)]
        public _30748 _30748 { get; set; }

        [JsonProperty("30749", NullValueHandling = NullValueHandling.Ignore)]
        public _30749 _30749 { get; set; }

        [JsonProperty("30750", NullValueHandling = NullValueHandling.Ignore)]
        public _30750 _30750 { get; set; }

        [JsonProperty("30751", NullValueHandling = NullValueHandling.Ignore)]
        public _30751 _30751 { get; set; }

        [JsonProperty("30752", NullValueHandling = NullValueHandling.Ignore)]
        public _30752 _30752 { get; set; }

        [JsonProperty("30753", NullValueHandling = NullValueHandling.Ignore)]
        public _30753 _30753 { get; set; }

        [JsonProperty("30754", NullValueHandling = NullValueHandling.Ignore)]
        public _30754 _30754 { get; set; }

        [JsonProperty("30755", NullValueHandling = NullValueHandling.Ignore)]
        public _30755 _30755 { get; set; }

        [JsonProperty("30756", NullValueHandling = NullValueHandling.Ignore)]
        public _30756 _30756 { get; set; }

        [JsonProperty("30757", NullValueHandling = NullValueHandling.Ignore)]
        public _30757 _30757 { get; set; }

        [JsonProperty("30758", NullValueHandling = NullValueHandling.Ignore)]
        public _30758 _30758 { get; set; }

        [JsonProperty("30759", NullValueHandling = NullValueHandling.Ignore)]
        public _30759 _30759 { get; set; }

        [JsonProperty("30760", NullValueHandling = NullValueHandling.Ignore)]
        public _30760 _30760 { get; set; }

        [JsonProperty("30764", NullValueHandling = NullValueHandling.Ignore)]
        public _30764 _30764 { get; set; }

        [JsonProperty("30761", NullValueHandling = NullValueHandling.Ignore)]
        public _30761 _30761 { get; set; }

        [JsonProperty("30762", NullValueHandling = NullValueHandling.Ignore)]
        public _30762 _30762 { get; set; }

        [JsonProperty("30763", NullValueHandling = NullValueHandling.Ignore)]
        public _30763 _30763 { get; set; }

        [JsonProperty("30765", NullValueHandling = NullValueHandling.Ignore)]
        public _30765 _30765 { get; set; }

        [JsonProperty("30766", NullValueHandling = NullValueHandling.Ignore)]
        public _30766 _30766 { get; set; }

        [JsonProperty("30786", NullValueHandling = NullValueHandling.Ignore)]
        public _30786 _30786 { get; set; }

        [JsonProperty("30768", NullValueHandling = NullValueHandling.Ignore)]
        public _30768 _30768 { get; set; }

        [JsonProperty("30769", NullValueHandling = NullValueHandling.Ignore)]
        public _30769 _30769 { get; set; }

        [JsonProperty("30770", NullValueHandling = NullValueHandling.Ignore)]
        public _30770 _30770 { get; set; }

        [JsonProperty("30394", NullValueHandling = NullValueHandling.Ignore)]
        public _30394 _30394 { get; set; }

        [JsonProperty("30363", NullValueHandling = NullValueHandling.Ignore)]
        public _30363 _30363 { get; set; }

        [JsonProperty("30366", NullValueHandling = NullValueHandling.Ignore)]
        public _30366 _30366 { get; set; }

        [JsonProperty("30367", NullValueHandling = NullValueHandling.Ignore)]
        public _30367 _30367 { get; set; }

        [JsonProperty("30368", NullValueHandling = NullValueHandling.Ignore)]
        public _30368 _30368 { get; set; }

        [JsonProperty("30369", NullValueHandling = NullValueHandling.Ignore)]
        public _30369 _30369 { get; set; }

        [JsonProperty("30370", NullValueHandling = NullValueHandling.Ignore)]
        public _30370 _30370 { get; set; }

        [JsonProperty("30372", NullValueHandling = NullValueHandling.Ignore)]
        public _30372 _30372 { get; set; }

        [JsonProperty("30374", NullValueHandling = NullValueHandling.Ignore)]
        public _30374 _30374 { get; set; }

        [JsonProperty("30375", NullValueHandling = NullValueHandling.Ignore)]
        public _30375 _30375 { get; set; }

        [JsonProperty("30376", NullValueHandling = NullValueHandling.Ignore)]
        public _30376 _30376 { get; set; }

        [JsonProperty("30377", NullValueHandling = NullValueHandling.Ignore)]
        public _30377 _30377 { get; set; }

        [JsonProperty("30378", NullValueHandling = NullValueHandling.Ignore)]
        public _30378 _30378 { get; set; }

        [JsonProperty("30379", NullValueHandling = NullValueHandling.Ignore)]
        public _30379 _30379 { get; set; }

        [JsonProperty("30778", NullValueHandling = NullValueHandling.Ignore)]
        public _30778 _30778 { get; set; }

        [JsonProperty("30779", NullValueHandling = NullValueHandling.Ignore)]
        public _30779 _30779 { get; set; }

        [JsonProperty("30380", NullValueHandling = NullValueHandling.Ignore)]
        public _30380 _30380 { get; set; }

        [JsonProperty("30381", NullValueHandling = NullValueHandling.Ignore)]
        public _30381 _30381 { get; set; }

        [JsonProperty("30382", NullValueHandling = NullValueHandling.Ignore)]
        public _30382 _30382 { get; set; }

        [JsonProperty("30383", NullValueHandling = NullValueHandling.Ignore)]
        public _30383 _30383 { get; set; }

        [JsonProperty("30385", NullValueHandling = NullValueHandling.Ignore)]
        public _30385 _30385 { get; set; }

        [JsonProperty("30387", NullValueHandling = NullValueHandling.Ignore)]
        public _30387 _30387 { get; set; }

        [JsonProperty("30388", NullValueHandling = NullValueHandling.Ignore)]
        public _30388 _30388 { get; set; }

        [JsonProperty("30389", NullValueHandling = NullValueHandling.Ignore)]
        public _30389 _30389 { get; set; }

        [JsonProperty("30390", NullValueHandling = NullValueHandling.Ignore)]
        public _30390 _30390 { get; set; }

        [JsonProperty("30780", NullValueHandling = NullValueHandling.Ignore)]
        public _30780 _30780 { get; set; }

        [JsonProperty("30392", NullValueHandling = NullValueHandling.Ignore)]
        public _30392 _30392 { get; set; }
    }















    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    public class _1394
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
    }


    public class _16639
    {
        public int Id { get; set; }
        public string AnswerStatus { get; set; }
        public int AnswerStatusId { get; set; }
        public string Comment { get; set; }
        public DateTime AnswerDate { get; set; }
        public object PreviousAnswerDate { get; set; }
        public Values Values { get; set; }
        public List<object> Comments { get; set; }
        public string UpdateSource { get; set; }
        public int RequestId { get; set; }
        public int RegisterId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30055
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30056
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30057
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30058
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30059
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30060
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30061
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public string Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30062
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30063
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30064
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30065
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30066
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30068
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30069
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30070
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30071
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30072
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30073
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30074
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30075
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30076
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30077
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30078
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30079
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30080
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30081
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30082
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30083
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30084
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30085
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30086
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30087
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30088
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30089
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30090
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30091
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30092
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30093
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30094
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30095
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30096
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30097
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30098
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30099
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30100
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30103
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30105
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30106
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30107
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30108
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30109
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30110
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30111
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30112
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30114
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30118
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30119
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public MatrixAnswers MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30120
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30121
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30123
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30124
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30125
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30126
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30127
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30128
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30130
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30131
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30132
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30133
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30134
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30135
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30138
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30323
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30324
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30325
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30326
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30327
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30328
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30329
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30330
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30331
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30332
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30333
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30334
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30335
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30337
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public MatrixAnswers MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30339
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30342
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30343
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30344
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30345
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30346
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30347
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30348
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30349
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30351
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30352
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30353
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30354
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30358
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30360
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30361
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30362
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30363
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30366
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30367
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30368
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30369
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30370
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30372
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30374
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30375
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30376
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30377
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30378
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30379
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30380
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30381
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30382
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30383
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30385
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30387
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30388
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30389
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30390
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30392
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30394
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30395
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30396
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30397
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30398
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30399
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30400
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30402
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30685
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30686
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30687
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30688
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30689
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30690
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30691
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30692
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30693
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30694
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30695
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30696
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30697
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30698
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30699
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30700
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30701
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30702
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30703
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30704
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30705
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30706
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30707
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30708
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30709
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30712
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30713
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30714
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30717
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30718
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30719
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30720
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30722
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30723
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30724
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30725
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30728
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30729
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30730
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30731
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30732
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30733
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30734
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30735
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30736
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30737
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30738
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30739
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30740
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30742
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30743
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30744
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30745
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30746
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public MatrixAnswers MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30747
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30748
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30749
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30750
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30751
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30752
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30753
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30754
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30755
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30756
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30757
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30758
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30759
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30760
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30761
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30762
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30763
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30764
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30765
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30766
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30768
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30769
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30770
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30771
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30772
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30773
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30774
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30775
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30777
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30778
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30779
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30780
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30781
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30783
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30785
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30786
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30787
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30788
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public List<int> TargetIds { get; set; }
        public int SourceQuestionId { get; set; }
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30789
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _30790
    {
        public int Id { get; set; }
        public int RegisterItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public bool IsPickList { get; set; }
        public List<object> Picklists { get; set; }
        public List<object> Documents { get; set; }
        public object MatrixAnswers { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6190
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6191
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6192
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6193
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6194
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6195
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6196
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6197
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6198
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6199
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6213
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6214
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6265
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6266
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6267
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6268
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6269
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class _6270
    {
        public int AnswerItemId { get; set; }
        public int ItemTypeId { get; set; }
        public object Answer { get; set; }
        public int CellId { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class Answers
    {
        [JsonProperty("16639", NullValueHandling = NullValueHandling.Ignore)]
        public _16639 _16639 { get; set; }
    }

    public class Child
    {
        public string NodeType { get; set; }
        public List<Child> Children { get; set; }
        public int TargetQuestionID { get; set; }
        public string ConditionType { get; set; }
        public string TargetValue { get; set; }
        public object TargetValue2 { get; set; }
    }

    public class Choice
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int? Value { get; set; }
        public int PickListId { get; set; }
        public int Order { get; set; }
        public object NameOtherLang { get; set; }
        public object Tooltip { get; set; }
        public object TooltipOtherLang { get; set; }
        public object ScoreLabel { get; set; }
        public object ScoreLabelColor { get; set; }
        public bool Deleted { get; set; }
        public int ClientId { get; set; }
        public object Data { get; set; }
        public object Status { get; set; }
        public bool Success { get; set; }
        public bool HasErrors { get; set; }
        public List<object> Messages { get; set; }
    }

    public class MatrixAnswers
    {
        [JsonProperty("6190", NullValueHandling = NullValueHandling.Ignore)]
        public _6190 _6190 { get; set; }

        [JsonProperty("6191", NullValueHandling = NullValueHandling.Ignore)]
        public _6191 _6191 { get; set; }

        [JsonProperty("6192", NullValueHandling = NullValueHandling.Ignore)]
        public _6192 _6192 { get; set; }

        [JsonProperty("6193", NullValueHandling = NullValueHandling.Ignore)]
        public _6193 _6193 { get; set; }

        [JsonProperty("6194", NullValueHandling = NullValueHandling.Ignore)]
        public _6194 _6194 { get; set; }

        [JsonProperty("6195", NullValueHandling = NullValueHandling.Ignore)]
        public _6195 _6195 { get; set; }

        [JsonProperty("6196", NullValueHandling = NullValueHandling.Ignore)]
        public _6196 _6196 { get; set; }

        [JsonProperty("6197", NullValueHandling = NullValueHandling.Ignore)]
        public _6197 _6197 { get; set; }

        [JsonProperty("6198", NullValueHandling = NullValueHandling.Ignore)]
        public _6198 _6198 { get; set; }

        [JsonProperty("6199", NullValueHandling = NullValueHandling.Ignore)]
        public _6199 _6199 { get; set; }

        [JsonProperty("6213", NullValueHandling = NullValueHandling.Ignore)]
        public _6213 _6213 { get; set; }

        [JsonProperty("6214", NullValueHandling = NullValueHandling.Ignore)]
        public _6214 _6214 { get; set; }

        [JsonProperty("6265", NullValueHandling = NullValueHandling.Ignore)]
        public _6265 _6265 { get; set; }

        [JsonProperty("6266", NullValueHandling = NullValueHandling.Ignore)]
        public _6266 _6266 { get; set; }

        [JsonProperty("6267", NullValueHandling = NullValueHandling.Ignore)]
        public _6267 _6267 { get; set; }

        [JsonProperty("6268", NullValueHandling = NullValueHandling.Ignore)]
        public _6268 _6268 { get; set; }

        [JsonProperty("6269", NullValueHandling = NullValueHandling.Ignore)]
        public _6269 _6269 { get; set; }

        [JsonProperty("6270", NullValueHandling = NullValueHandling.Ignore)]
        public _6270 _6270 { get; set; }
    }

    


}
