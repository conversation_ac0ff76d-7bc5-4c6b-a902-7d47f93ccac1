﻿using MicroSaasWebApi.Models.AlphaCCO;

namespace MicroSaasWebApi.Models.PXW.AppSettings
{
    public class AppSettings
    {
        public Properties? Properties { get; set; }
        public Configurations? Configurations { get; set; }
        public Tenants? Tenants { get; set; }
        public Logging? Logging { get; set; }
        public MicrosoftIdentityModelLogging? MicrosoftIdentityModelLogging { get; set; }
        public string? AllowedHosts { get; set; }
    }
    public class Properties
    {
        public string? AppName { get; set; }
        public string? Version { get; set; }
    }
    public class Configurations
    {
        public Endpoints? Endpoints { get; set; }
    }
    public class Endpoints
    {
        public PortfolioDataApiEndpoints? PortfolioDataApiEndpoints { get; set; }
        public PortfolioDocumentApiEndpoints? PortfolioDocumentApiEndpoints { get; set; }
        public PortfolioPermissionApiEndpoints? PortfolioPermissionApiEndpoints { get; set; }
        public PortfolioDataInterchangeApiEndpoints? PortfolioDataInterchangeApiEndpoints { get; set; }
    }
    public class PortfolioDataApiEndpoints
    {
        public Endpoint? CreatePrivateSecurity { get; set; }
        public Endpoint? RetrievePrivateSecurityByID { get; set; }
        public Endpoint? ListPrivateSecuritiesByProfileID { get; set; }
        public Endpoint? PartiallyUpdatePrivateSecurityByID { get; set; }
        public Endpoint? CreatePrivateTransaction { get; set; }
        public Endpoint? RetrievePrivateTransactionByID { get; set; }
        public Endpoint? ListPrivateTransactionsBySecurityID { get; set; }
        public Endpoint? PartiallyUpdatePrivateTransactionByID { get; set; }
        public Endpoint? DestroyPrivateTransactionByID { get; set; }
        public Endpoint? CreatePrivateSecurityPrice { get; set; }
        public Endpoint? RetrievePrivateSecurityPriceByID { get; set; }
        public Endpoint? ListPrivateSecurityPricesBySecurityID { get; set; }
        public Endpoint? PartiallyUpdatePrivateSecurityPriceByID { get; set; }
        public Endpoint? DestroyPrivateSecurityPriceByID { get; set; }
        public Endpoint? RetrieveAssetClass { get; set; }
        public Endpoint? RetrieveSecurityCode { get; set; }
        public Endpoint? RetrieveSecurityTypes { get; set; }
        public Endpoint? RetrieveDataPipelineStatus { get; set; }
        public Endpoint? ExportPrivateSecuritiesTemplate { get; set; }
        public Endpoint? UploadPrivateSecuritiesTemplate { get; set; }


        public Endpoint? CurrenciesList { get; set; }
        public Endpoint? ProfilesAccountsSummaryRetrieve { get; set; }
        public Endpoint? ProfilesAsOfDatesRetrieve { get; set; }
        public Endpoint? ProfilesCustodianAccountCodesRetrieve { get; set; }
        public Endpoint? ProfilesDashboardLineGraphRetrieve { get; set; }
        public Endpoint? ProfilesDashboardSummaryRetrieve { get; set; }
        public Endpoint? ProfilesPortfolioSummaryRetrieve { get; set; }
        public Endpoint? ProfilesFundingsRetrieve { get; set; }
        public Endpoint? ProfilesHoldingsRetrieve { get; set; }
        public Endpoint? ProfilesAllHoldingsRetrieve { get; set; }
        public Endpoint? ProfilesPieChartByAccountRetrieve { get; set; }
        public Endpoint? ProfilesPieChartByAssetClassRetrieve { get; set; }
        public Endpoint? ProfilesPieChartByCurrencyRetrieve { get; set; }
        public Endpoint? ProfilesPieChartsRetrieve { get; set; }
        public Endpoint? ProfilesTopHoldingsRetrieve { get; set; }
        public Endpoint? ProfilesTransactionsRetrieve { get; set; }
        public Endpoint? ProfilesAccountsList { get; set; }
        public Endpoint? ProfilesAccountsHoldingsRetrieve { get; set; }
        public Endpoint? ProfilesAccountsTransactionsRetrieve { get; set; }
        public Endpoint? LogUserLogin { get; set; }
        public Endpoint? LogUserLogout { get; set; }
        public Endpoint? AccountLevelPerformance { get; set; }
        public Endpoint? PresetDateRanges { get; set; }
        public Endpoint? RorTable { get; set; }
        public Endpoint? ListSavedViews { get; set; }
        public Endpoint? CreateSavedView { get; set; }
        public Endpoint? ReadSavedView { get; set; }
        public Endpoint? UpdateSavedView { get; set; }
        public Endpoint? PartialUpdateSavedView { get; set; }
        public Endpoint? DeleteSavedView { get; set; }
        public Endpoint? DashboardData { get; set; }
        public Endpoint? CheckSecurityCodeAvailability { get; set; }
    }

    public class PortfolioDocumentApiEndpoints
    {
        public Endpoint? ListDocuments { get; set; }
        public Endpoint? SearchDocuments { get; set; }
        public Endpoint? DownloadDocuments { get; set; }
        public Endpoint? UploadDocuments { get; set; }
        public Endpoint? DeleteDocuments { get; set; }
        public Endpoint? MoveDocuments { get; set; }
        public Endpoint? CreateRole { get; set; }
        public Endpoint? DeleteRole { get; set; }
        public Endpoint? ListRoles { get; set; }
        public Endpoint? GetRole { get; set; }
        public Endpoint? CreateFolder { get; set; }
        public Endpoint? ActivateExternalUser { get; set; }
        public Endpoint? CreateExternalUser { get; set; }
        public Endpoint? DeactivateExternalUser { get; set; }
        public Endpoint? InviteExternalUser { get; set; }
        public Endpoint? ListOrganizations { get; set; }
        public Endpoint? CreateUser { get; set; }
        public Endpoint? UpdateUser { get; set; }
        public Endpoint? GetUser { get; set; }
        public Endpoint? DeleteUser { get; set; }
        public Endpoint? CreateGroup { get; set; }
        public Endpoint? UpdateGroup { get; set; }
        public Endpoint? GetGroup { get; set; }
        public Endpoint? DeleteGroup { get; set; }
        public Endpoint? GetDocumentCategories { get; set; }
        public Endpoint? GetUserPermissions { get; set; }
        public Endpoint? SearchUsers { get; set; }
        public Endpoint? CreateShare { get; set; }
        public Endpoint? UpdateShare { get; set; }
        public Endpoint? GetShare { get; set; }
        public Endpoint? AddDocumentCategory { get; set; }
        public Endpoint? DeleteDocumentCategory { get; set; }
        public Endpoint? GetDocumentActivityFeed { get; set; }
        public Endpoint? UpdateDocumentMetadatas { get; set; }
        public Endpoint? ListNotifications { get; set; }
        public Endpoint? ListUnreadNotifications { get; set; }
        public Endpoint? UserSubscriptionNotifications { get; set; }
        public Endpoint? GetAllShares { get; set; }
        public Endpoint? AddComment { get; set; }
        public Endpoint? GetComment { get; set; }
        public Endpoint? ListComments { get; set; }
        public Endpoint? DeleteComment { get; set; }
        public Endpoint? UpdateComment { get; set; }
        public Endpoint? ListUsers { get; set; }
        public Endpoint? GetCommentUnreadCount { get; set; }
        public Endpoint? SetUserEmailNotification { get; set; }
    }

    public class PortfolioPermissionApiEndpoints
    {
        public Endpoint? ListUsers { get; set; }
        public Endpoint? GetUser { get; set; }
        public Endpoint? CreateUser { get; set; }
        public Endpoint? SetUserLoginDatetime { get; set; }
        public Endpoint? ProfilesAccountsSearch { get; set; }
        public Endpoint? UpdateUser { get; set; }
        public Endpoint? DeleteUser { get; set; }
        public Endpoint? SendInviteUser { get; set; }
        public Endpoint? CurrentUser { get; set; }
        public Endpoint? ListUserProfiles { get; set; }
        public Endpoint? AssignUserProfile { get; set; }
        public Endpoint? ListUserRoles { get; set; }
        public Endpoint? AssignUserRole { get; set; }
        public Endpoint? ListProfiles { get; set; }
        public Endpoint? GetProfile { get; set; }
        public Endpoint? UpdateProfile { get; set; }
        public Endpoint? DeleteProfile { get; set; }
        public Endpoint? CreateProfile { get; set; }
        public Endpoint? ListProfileAccounts { get; set; }
        public Endpoint? AssignProfileAccount { get; set; }
        public Endpoint? ListAccounts { get; set; }
        public Endpoint? GetAccount { get; set; }
        public Endpoint? CreateAccount { get; set; }
        public Endpoint? DeleteAccount { get; set; }
        public Endpoint? UpdateAccount { get; set; }
        public Endpoint? ListRoles { get; set; }
        public Endpoint? GetRole { get; set; }
        public Endpoint? DeleteRole { get; set; }
        public Endpoint? UpdateRole { get; set; }
        public Endpoint? CreateRole { get; set; }
        public Endpoint? ListProfileGroups { get; set; }
        public Endpoint? CreateProfileGroup { get; set; }
        public Endpoint? GetProfileGroup { get; set; }
        public Endpoint? PartiallyUpdateProfileGroup { get; set; }
        public Endpoint? DeleteProfileGroup { get; set; }
        public Endpoint? AssignProfilesToProfileGroup { get; set; }
        public Endpoint? UnassignProfilesToProfileGroup { get; set; }
        public Endpoint? ListUserProfileGroups { get; set; }
        public Endpoint? AssignProfileGroupToUser { get; set; }
        public Endpoint? UnassignProfileGroupToUser { get; set; }
        public Endpoint? BatchAssignUsersToProfileGroup { get; set; }
        public Endpoint? BatchAssignUsersToProfile { get; set; }
    }

    public class PortfolioDataInterchangeApiEndpoints
    {
        public Endpoint? ListWorkflows { get; set; }
        public Endpoint? CreateWorkflow { get; set; }
        public Endpoint? GetWorkflow { get; set; }
        public Endpoint? GetWorkflowSteps { get; set; }
        public Endpoint? UpdateWorkflow { get; set; }
        public Endpoint? DeleteWorkflow { get; set; }
        public Endpoint? ActivateWorkflow { get; set; }
        public Endpoint? ArchiveWorkflow { get; set; }
        public Endpoint? ListWorkflowVersions { get; set; }
        public Endpoint? GetWorkflowPreview { get; set; }
        public Endpoint? GetWorkflowVersionPreview { get; set; }
        public Endpoint? SetWorkflowPublicVersion { get; set; }
        public Endpoint? UploadWorkflowVersion { get; set; }
        public Endpoint? GetWorkflowYAML { get; set; }
        public Endpoint? GetWorkflowVersionYAML { get; set; }
        public Endpoint? ListWorkflowInstances { get; set; }
        public Endpoint? CreateWorkflowInstance { get; set; }
        public Endpoint? GetWorkflowInstance { get; set; }
        public Endpoint? UpdateWorkflowInstance { get; set; }
        public Endpoint? GetWorkflowInstanceLogs { get; set; }
        public Endpoint? GetWorkflowInstanceComments { get; set; }
        public Endpoint? CreateWorkflowInstanceComment { get; set; }
        public Endpoint? GetWorkflowInstanceData { get; set; }
        public Endpoint? CreateWorkflowInstanceData { get; set; }
        public Endpoint? UpdateWorkflowInstanceData { get; set; }
        public Endpoint? GetWorkflowInstanceQuestions { get; set; }
        public Endpoint? GetWorkflowInstanceSteps { get; set; }
        public Endpoint? AssignWorkflowInstance { get; set; }
        public Endpoint? GetWorkflowInstanceFile { get; set; }
    }

    public class Endpoint
    {
        public string? URL { get; set; }
    }

    #region Tenants
    public class Tenants : List<Tenant>
    {
    }

    public class Tenant
    {
        public int TenantId { get; set; }
        public string? TenantName { get; set; }
        public string? TenantGuid { get; set; }
        public Settings? Settings { get; set; }
    }
    public class Settings
    {
        public AzureAdB2C? AzureAdB2C { get; set; }
        public AlphaCCOWorkflows? AlphaCCOWorkflows { get; set; }
        public PortfolioDataApi? PortfolioDataApi { get; set; }
        public PortfolioDocumentApi? PortfolioDocumentApi { get; set; }
        public PortfolioPermissionApi? PortfolioPermissionApi { get; set; }
        public PortfolioDataInterchangeApi? PortfolioDataInterchangeApi { get; set; }
        public AlphaCC0? AlphaCC0 { get; set; }
        public Pages? Pages { get; set; }
        public Emails? Emails { get; set; }
        public Documents? Documents { get; set; }
    }
    public class AzureAdB2C
    {
        public string? Instance { get; set; }
        public string? Domain { get; set; }
        public string? TenantId { get; set; }
        public string? ClientId { get; set; }
        public string? CallbackPath { get; set; }
        public string? Scopes { get; set; }
        public string? SignUpSignInPolicyId { get; set; }
        public string? SignedOutCallbackPath { get; set; }
        public string? ResetPasswordPolicyId { get; set; }
        public string? EditProfilePolicyId { get; set; }
        public bool? EnablePiiLogging { get; set; }
        public string? ApplicationIdUri { get; set; }
        public string? JwksUri { get; set; }
        public string? MetadataUrl { get; set; }
        public string? Issuer { get; set; }
        public string? OrganizationId { get; set; }
        public string? BaseURL { get; set; }
        public string? APIMKey { get; set; }
        public string? APIMSecretKey { get; set; }
        public string? GETMemberListURL { get; set; }
        public string? GETMemberURL { get; set; }
        public string? GETProfilesForUserURL { get; set; }
        public string? GETAccountsForProfileURL { get; set; }
        public string? GETMembersBriefURL { get; set; }
        public string? UPDATEMemberURL { get; set; }
        public string? BearerTokenBaseURL { get; set; }
        public string? BearerTokenTenant { get; set; }
        public string? BearerTokenClientID { get; set; }
        public string? BearerTokenClientSecret { get; set; }
        public string? BearerTokenGrantType { get; set; }
        public string? BearerTokenScope { get; set; }
        public string? BearerTokenApplicationName { get; set; }
        public string? RedirectBaseURL { get; set; }
        public string? RedirectParam { get; set; }
        public string? RedirectClientID { get; set; }
        public string? RedirectMemberURI { get; set; }
    }
    public class AlphaCCOWorkflows : List<AlphaCCOWorkflow>
    {
    }
    public class PortfolioDataApi
    {
        public string? Domain { get; set; }
    }
    public class PortfolioDocumentApi
    {
        public string? Domain { get; set; }
    }
    public class PortfolioPermissionApi
    {
        public string? Domain { get; set; }
    }
    public class PortfolioDataInterchangeApi
    {
        public string? Domain { get; set; }
    }
    public class AlphaCC0
    {
        public string? AlphaCCOBaseURL { get; set; }
        public string? AlphaCCOTenantID { get; set; }
        public string? AlphaCCOHost { get; set; }
        public string? AlphaCCOHostkey { get; set; }
        public string? AlphaCCOComponentHost { get; set; }
        public string? AlphaCCOComponentHostKey { get; set; }
        public string? AlphaCCOAuthenticationURL { get; set; }
    }
    public class Pages
    {
        public string? DashboardURL { get; set; }
        public string? SurveyPageURL { get; set; }
        public string? AccountHoldingPageURL { get; set; }
        public string? AccountTransactionPageURL { get; set; }
        public string? AuthenticationPageURL { get; set; }
        public string? ProfileSettingPageURL { get; set; }
        public string? DocumentPageURL { get; set; }
        public string? SurveySubmitPageReturnURL { get; set; }
        public string? RegistrationEmailTemplateLoaction { get; set; }
        public string? ResetPasswordEmailTemplateLoaction { get; set; }
    }
    public class Emails
    {
        public string? EmailAddressForDocumentImportErrorMessage { get; set; }
        public string? DocumentImportErrorMessageEmailTemplate { get; set; }
        public string? DocumentNotificationEmailTemplate { get; set; }
        public string? DocumentNotificationSubject { get; set; }
        public string? DocumentNotificationDocumentCountPhase { get; set; }
    }
    public class Documents
    {
        public string? SharePointDocumentSiteUrl { get; set; }
        public string? SharePointDocumentClientName { get; set; }
        public string? SharePointDocumentClientId { get; set; }
        public string? SharePointDocumentClientSecret { get; set; }
        public string? SharePointDocumentLandingDocumentLibrary { get; set; }
        public string? SharePointDocumentLandingFolder { get; set; }
        public string? SharePointDocumentFinalDocumentLibrary { get; set; }
        public string? SharePointDocumentFinalFolder { get; set; }
        public DocumentTypes? DocumentTypes { get; set; }
    }

    public class DocumentTypes : List<DocumentType> { }
    #endregion

    #region Logging
    public class Logging
    {
        public LogLevel? LogLevel { get; set; }
    }

    public class LogLevel
    {
        public string? Default { get; set; }
        public string? Microsoft { get; set; }
        public string? MicrosoftHostingLifetime { get; set; }
        public string? MicrosoftAspNetCore { get; set; }
        public string? MicrosoftIdentityModel { get; set; }
        public string? System { get; set; }
    }
    #endregion

    #region MicrosoftIdentityModelLogging
    public class MicrosoftIdentityModelLogging
    {
        public LogLevel? LogLevel { get; set; }
        public bool? IncludeScopes { get; set; }
        public bool? LogToConsole { get; set; }
        public bool? LogToTrace { get; set; }
        public bool? DisplayPII { get; set; }
    }
    #endregion
}
