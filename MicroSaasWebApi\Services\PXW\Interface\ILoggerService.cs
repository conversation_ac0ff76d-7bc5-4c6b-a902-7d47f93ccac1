﻿namespace MicroSaasWebApi.Services.PXW.Interface
{
    public interface ILoggerService
    {
        Task LogInformationAsync(string message);
        Task LogInformationAndRequestAsync(string message);
        Task LogRequestInformationAsync();
        Task LogErrorAsync(Exception exception, string message);
        Task LogTraceAsync(string message);
        Task SetResponseAndLogAsync(int statusCode, string message, Exception? e = null);
    }
}
