﻿using System.Text;

namespace MicroSaasWebApi.Properties.PXW
{
    public static class CookieHelper
    {

        public static string CreateSecureCookieString(string cookieName, string cookieValue, int expirationMinutes, string domain = null)
        {
            var expires = DateTime.UtcNow.AddMinutes(expirationMinutes).ToString("R"); // cookie expiration


            var cookieString = $"{cookieName}={cookieValue}; Path=/api; Expires={expires}; SameSite=Strict; Secure; HttpOnly";

            if (!string.IsNullOrEmpty(domain))
            {
                cookieString += $" Domain={domain};";
            }

            return cookieString;
        }
        public static IEnumerable<string> CreateCookieStringFromCookieHeaderArray(string[] cookieHeaders, int expirationExtensionMinutes = 30, string domain = null)
        {
            var updatedCookies = new List<string>();

            foreach (var header in cookieHeaders)
            {
                // Parse the cookie name and value from the header
                var cookieParts = header.Split(';')[0].Split('=');
                if (cookieParts.Length < 2) continue; // Skip if the header is not a valid cookie

                var cookieName = cookieParts[0].Trim();
                var cookieValue = cookieParts[1].Trim();

                // Create the new secure cookie string
                var newCookie = CreateSecureCookieString(cookieName, cookieValue, expirationExtensionMinutes, domain);
                updatedCookies.Add(newCookie);
            }

            return updatedCookies;
        }

        public static string[] ModifyCookiesForSecurity(string[] cookieValues, bool isRequestSecure)
        {
            for (int i = 0; i < cookieValues.Length; i++)
            {
                var cookie = cookieValues[i];
                // Parse the cookie and rebuild it with the necessary attributes
                var cookieBuilder = new StringBuilder(cookie);

                // Add HttpOnly if not already present
                if (!cookie.ToLower().Contains("httponly"))
                {
                    cookieBuilder.Append("; HttpOnly");
                }

                // Add Secure if not already present and if the request is over HTTPS
                if (!cookie.ToLower().Contains("secure") && isRequestSecure)
                {
                    cookieBuilder.Append("; Secure");
                }
                // Add SameSite=Strict
                cookieBuilder.Append("; SameSite=Strict");
                // Add path and expiration
                cookieBuilder.Append("; Path=/api");
                cookieBuilder.Append("; Max-Age=1800");

                // Update the cookie in the array
                cookieValues[i] = cookieBuilder.ToString();
            }

            return cookieValues;
        }


    }
}
