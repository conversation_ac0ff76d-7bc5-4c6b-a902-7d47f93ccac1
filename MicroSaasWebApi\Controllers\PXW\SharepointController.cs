﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.Profile;
using MicroSaasWebApi.Models.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using SharePointAPI;
using Swashbuckle.AspNetCore.Filters;
using System.IO.Compression;
using MicroSaasWebApi.Models.PXW.AppSettings;

namespace MicroSaasWebApi.Controllers.PXW
{
    [Route("api/PXW/[controller]")]
    [ApiController]
    [EnableCors("AllowAll")]
    public class SharepointController : BaseController
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        public SharepointController(IWebHostEnvironment webHostEnvironment, IBaseService baseService,
            ILoggerService loggerService) : base(baseService, loggerService)
        {
            _webHostEnvironment = webHostEnvironment;
        }

        [HttpPost("GetDocuments")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> GetDocuments()
        {
            try
            {
                // Log information about the request
                await _loggerService.LogRequestInformationAsync();
                _baseService.ReadRequestBody();
                // Access payloadString from CommonService
                string payloadString = _baseService.PayloadString;
                // Convert to JObject
                Newtonsoft.Json.Linq.JObject contentRequest = Newtonsoft.Json.Linq.JObject.Parse(payloadString);
                GetDocumentsRequest inpt = contentRequest.ToObject<GetDocumentsRequest>();
                if (inpt.accountIds != null && inpt.accountIds.Count > 0)
                {
                    string accountIds = String.Join(",", inpt.accountIds);
                    Tenant FilteredTenant = _baseService.GetFilteredTenant();
                    SharePointAPI.General.SharePointAPISetting(FilteredTenant?.Settings?.Documents?.SharePointDocumentSiteUrl,
                    FilteredTenant?.Settings?.Documents?.SharePointDocumentClientId,
                    FilteredTenant?.Settings?.Documents?.SharePointDocumentClientSecret,
                    FilteredTenant?.Settings?.Documents?.SharePointDocumentLandingDocumentLibrary,
                    FilteredTenant?.Settings?.Documents?.SharePointDocumentLandingFolder,
                    FilteredTenant?.Settings?.Documents?.SharePointDocumentFinalDocumentLibrary,
                    FilteredTenant?.Settings?.Documents?.SharePointDocumentFinalFolder);

                    SharePointAPI.GetFiles obj = new SharePointAPI.GetFiles();
                    obj.objColumns = new List<string>();
                    obj.objColumns.Add("AccountId;Text;Bg;" + accountIds);
                    List<FileDetail> fileDetails = new List<FileDetail>();
                    if (!string.IsNullOrEmpty(accountIds))
                    {
                        var list = obj.GetAllDcoumentsInaList();
                        DocumentType dt;
                        if (list.Count() > 0)
                        {
                            foreach (var item in list)
                            {
                                var filename = item.FieldValues["FileLeafRef"];
                                var AccountId = item.FieldValues["AccountId"];
                                //  var fileurl = General.SiteUrl + item.FieldValues["FileRef"];
                                var Typetag = item.FieldValues["TypeTag"];
                                var Date = item.FieldValues["Date"];
                                var FileId = item.FieldValues["FileId"];
                                dt = FilteredTenant?.Settings?.Documents?.DocumentTypes?.FirstOrDefault(d => d.Name.ToLower().Equals(Typetag.ToString().ToLower()));
                                if (dt != null && AccountId != null)
                                {
                                    Typetag = dt.DisplayName;
                                    fileDetails.Add(new FileDetail()
                                    {
                                        AccountId = AccountId.ToString(),
                                        Date = Convert.ToDateTime(Date),
                                        FileId = FileId,
                                        Filename = filename,
                                        TypeTag = Typetag,
                                        TypeDesc = dt.Description
                                    });
                                }
                            }

                        }
                    }
                    return Ok(fileDetails);
                }
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("DownloadDocument")]
        //[SwaggerOperation("ProfilesList")]
        //[Produces("application/json")]
        //[Consumes("application/json")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DownloadDocument()
        {
            try
            {
                // Log information about the request
                await _loggerService.LogRequestInformationAsync();
                _baseService.ReadRequestBody();
                // Access payloadString from CommonService
                string payloadString = _baseService.PayloadString;
                // Convert to JObject
                Newtonsoft.Json.Linq.JObject contentRequest = Newtonsoft.Json.Linq.JObject.Parse(payloadString);
                DownloadDocumentRequest input = contentRequest.ToObject<DownloadDocumentRequest>();

                if (input == null || string.IsNullOrEmpty(input.filename))
                {
                    return BadRequest("FileName is required");
                }

                string filename = input.filename;
                Tenant FilteredTenant = _baseService.GetFilteredTenant();
                var sharePointSettings = FilteredTenant?.Settings?.Documents;
                if (sharePointSettings == null)
                {
                    return BadRequest("SharePoint settings not found");
                }

                SharePointAPI.General.SharePointAPISetting(sharePointSettings.SharePointDocumentSiteUrl,
                                                           sharePointSettings.SharePointDocumentClientId,
                                                           sharePointSettings.SharePointDocumentClientSecret,
                                                           sharePointSettings.SharePointDocumentLandingDocumentLibrary,
                                                           sharePointSettings.SharePointDocumentLandingFolder,
                                                           sharePointSettings.SharePointDocumentFinalDocumentLibrary,
                                                           sharePointSettings.SharePointDocumentFinalFolder);

                var ms = GetFiles.DownloadFilesFromSharePoint(filename);

                if (ms == null)
                {
                    return NotFound("File not found");
                }
                HttpResponse response = _baseService.HttpContextAccessor.HttpContext?.Response;
                response.ContentType = "application/pdf";
                response.Headers.Add("Content-Disposition", string.Format("attachment; filename={0}", filename));

                await response.Body.WriteAsync(ms.ToArray(), 0, (int)ms.Length);
                return new EmptyResult();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("DownloadDocuments")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(RequestBody), typeof(RequestBodyExample))]
        public async Task<IActionResult> DownloadDocuments()
        {
            try
            {
                // Log information about the request
                await _loggerService.LogRequestInformationAsync();
                _baseService.ReadRequestBody();
                // Access payloadString from CommonService
                string payloadString = _baseService.PayloadString;
                // Convert to JObject
                Newtonsoft.Json.Linq.JObject contentRequest = Newtonsoft.Json.Linq.JObject.Parse(payloadString);
                DownloadDocumentsRequest input = contentRequest.ToObject<DownloadDocumentsRequest>();

                if (input == null || input.filenames == null || input.filenames.Count == 0)
                {
                    return BadRequest("FileNames are required");
                }
                Tenant FilteredTenant = _baseService.GetFilteredTenant();
                var sharePointSettings = FilteredTenant?.Settings?.Documents;
                if (sharePointSettings == null)
                {
                    return BadRequest("SharePoint settings not found");
                }

                SharePointAPI.General.SharePointAPISetting(sharePointSettings.SharePointDocumentSiteUrl,
                                                           sharePointSettings.SharePointDocumentClientId,
                                                           sharePointSettings.SharePointDocumentClientSecret,
                                                           sharePointSettings.SharePointDocumentLandingDocumentLibrary,
                                                           sharePointSettings.SharePointDocumentLandingFolder,
                                                           sharePointSettings.SharePointDocumentFinalDocumentLibrary,
                                                           sharePointSettings.SharePointDocumentFinalFolder);

                string folder = Path.Combine("pxw", "assets", "documents", Path.GetRandomFileName());

                if (!Directory.Exists(Path.Combine(_webHostEnvironment.ContentRootPath, folder)))
                {
                    Directory.CreateDirectory(Path.Combine(_webHostEnvironment.ContentRootPath, folder));
                }

                List<string> fileNameList = new List<string>();
                foreach (string item in input.filenames)
                {
                    var ms = GetFiles.DownloadFilesFromSharePoint(item);
                    string filePath = Path.Combine(_webHostEnvironment.ContentRootPath, folder, item);
                    using (FileStream file = new FileStream(filePath, FileMode.Create))
                    {
                        ms.CopyTo(file);
                    }
                    fileNameList.Add(filePath);
                }

                var memoryStream = new MemoryStream();
                using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
                {
                    foreach (var filePath in fileNameList)
                    {
                        var entry = archive.CreateEntry(Path.GetFileName(filePath));
                        using (var fileStream = new FileStream(filePath, FileMode.Open))
                        using (var entryStream = entry.Open())
                        {
                            fileStream.CopyTo(entryStream);
                        }
                    }
                }

                memoryStream.Position = 0;
                string zipName = $"{sharePointSettings.SharePointDocumentClientName}_Documents_{DateTime.Now:yyyy_MM_dd_hh_mm_ss}.zip";

                try
                {
                    Directory.Delete(Path.Combine(_webHostEnvironment.ContentRootPath, folder), true);
                }
                catch (Exception ex)
                {
                    // Log the exception or handle it accordingly
                }

                return File(memoryStream, "application/zip", zipName);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        //Commented Temporarily.
        //[HttpGet, HttpPost]
        //[Route("api/accountprofile/DocumentProcess")]
        //public IHttpActionResult DocumentProcess()
        //{
        //    SQL sql = new SQL();
        //    Domain d = DomainLanguageHelper.GetDomain(sql, HttpContext.Current);
        //    Language l = DomainLanguageHelper.GetLanguage(sql, d, HttpContext.Current);
        //    AdvantageModuleEngine ame = new AdvantageModuleEngine(sql, d.DomainID, l.ID);
        //    APISetting APISettings = ame.GetAllPublishedObjects<APISetting>().First();
        //    List<DocumentType> DocumentTypes = ame.GetAllPublishedObjects<DocumentType>();
        //    if (APISettings != null)
        //    {
        //        if (!string.IsNullOrEmpty(HttpContext.Current.Request.Params["token"]))
        //        {
        //            string token = HttpContext.Current.Request.Params["token"];
        //            try
        //            {
        //                DateTime requesttime = Convert.ToDateTime(Encryption.RijndaelEncryption.Decrypt(token, APISettings.APISecurityKey));
        //                if (requesttime > DateTime.Now.ToUniversalTime().AddMinutes(-15))
        //                {
        //                    SharePointAPI.General.SharePointAPISetting(APISettings.SharePointDocumentSiteUrl,
        //                                    APISettings.SharePointDocumentClientId,
        //                                    APISettings.SharePointDocumentClientSecret,
        //                                    APISettings.SharePointDocumentLandingDocumentLibrary,
        //                                    APISettings.SharePointDocumentLandingFolder,
        //                                    APISettings.SharePointDocumentFinalDocumentLibrary,
        //                                    APISettings.SharePointDocumentFinalFolder);
        //                    SharePointAPI.GetFiles fileobj = new SharePointAPI.GetFiles();
        //                    StringBuilder errormsg = new StringBuilder();
        //                    fileobj.objColumns = new List<string>();
        //                    var list = fileobj.GetFileList(APISettings.SharePointDocumentLandingFolder);
        //                    List<SharePointAPI.UploadFile> fileList = new List<UploadFile>();
        //                    List<string> AccountIds = new List<string>();

        //                    foreach (Microsoft.SharePoint.Client.ListItem item in list)
        //                    {
        //                        ///loop through all files and get attribitues from file nam
        //                        ///
        //                        SharePointAPI.UploadFile obj = new SharePointAPI.UploadFile();
        //                        string filename = item["FileRef"].ToString().Split('/').LastOrDefault();
        //                        List<string> fileproperties = filename.Split('_').ToList();
        //                        if (fileproperties.Count >= 5)
        //                        {
        //                            string accounttype = fileproperties[1];
        //                            obj.AccountId = fileproperties[2];
        //                            if (fileproperties[4].IndexOf('.') > 0)
        //                                obj.FileId = fileproperties[4].Substring(0, fileproperties[4].IndexOf('.'));
        //                            else
        //                                obj.FileId = fileproperties[4];
        //                            obj.Date = GetDate(fileproperties[0]);
        //                            obj.Mandatory = fileproperties[3] == "1" ? "Yes" : "No";
        //                            obj.Type = accounttype;

        //                            if (DocumentTypes.FirstOrDefault(ft => ft.Name.ToLower().Equals(accounttype.ToLower())) == null)
        //                                accounttype = "Others";

        //                            obj.customField = new List<CustomField>() {
        //                    new CustomField()
        //                    {
        //                        FieldKey = "FileId",
        //                        Value = obj.FileId
        //                    },
        //                     new CustomField()
        //                    {
        //                        FieldKey = "AccountId",
        //                        Value = obj.AccountId
        //                    },
        //                      new CustomField()
        //                    {
        //                        FieldKey = "TypeTag",
        //                        Value = accounttype
        //                    },
        //                       new CustomField()
        //                    {
        //                        FieldKey = "Date",
        //                        Value = obj.Date
        //                    },
        //                       new CustomField()
        //                    {
        //                        FieldKey = "Mandatory",
        //                        Value = obj.Mandatory
        //                    }
        //                };

        //                            fileList.Add(obj);
        //                            string fileid = item.FieldValues["UniqueId"].ToString();

        //                            var res = obj.UploadFileByPath(new Guid(fileid.Split(':')[0]), filename.Substring(0, filename.IndexOf('.')), filename.Substring(filename.IndexOf('.') + 1));
        //                            if (res.Item1 == "success")
        //                            {
        //                                string accountid = obj.AccountId.Length > MINIM_ACCOUNTCODE_LENGTH ? obj.AccountId.Substring(0, MINIM_ACCOUNTCODE_LENGTH) : obj.AccountId;
        //                                if (!AccountIds.Contains(accountid))
        //                                {
        //                                    AccountIds.Add(accountid);
        //                                }
        //                            }
        //                            else
        //                            {
        //                                errormsg.AppendFormat("File {0} failed upload, error message {1}.<br/>", item["FileRef"], res.Item2);
        //                            }
        //                        }
        //                        else
        //                        {
        //                            ///error filename is not correct
        //                            errormsg.AppendFormat("File {0} name not contains all required elements.<br/>", item["FileRef"]);
        //                        }


        //                    }
        //                    errormsg.AppendFormat("Get email function start at {0}<br/>", DateTime.Now);

        //                    List<AccountsEmail> emailList = new BusinessEngine(HttpContext.Current, sql, d.DomainID, l.ID).GetEmails(AccountIds, fileList);



        //                    foreach (AccountsEmail email in emailList)
        //                    {
        //                        //  string templatepath = System.Web.HttpContext.Current.Server.MapPath(APISettings.DocumentNotificationEmailTemplate);
        //                        //  string str = File.ReadAllText(templatepath);

        //                        string str = APISettings.DocumentNotificationEmailTemplate;
        //                        StringBuilder str2 = new StringBuilder();
        //                        foreach (var fileCount in email.AccountId_ProfileName.GroupBy(pn => pn.ProfileName).Select(g => new { Count = email.AccountId_ProfileName.Where(pn => pn.ProfileName.Equals(g.Key)).Sum(pn => pn.fileCount), Metric = g.Key }))
        //                        {
        //                            str2.AppendFormat("{0} {1} {2}<br/>", fileCount.Count, APISettings.DocumentNotificationDocumentCountPhase, fileCount.Metric);
        //                        }
        //                        str = str.Replace("{{filecount}}", str2.ToString());
        //                        str = str.Replace("{{documentURL}}", "https://" + d.PrimaryUrlPath + "/" + APISettings.DocumentPageURL);
        //                        str = str.Replace("{{firstname}}", email.GreetingName);
        //                        str = str.Replace("{{domainpath}}", d.PrimaryUrlPath);

        //                        MailMessage message = new MailMessage();

        //                        message.To.Add(new MailAddress(email.Email));
        //                        message.Subject = APISettings.DocumentNotificationSubject;
        //                        message.IsBodyHtml = true;
        //                        message.Body = str.ToString();
        //                        try
        //                        {
        //                            new EmailMessage("").SendGridEmail(message, APISettings.SendGridKey, APISettings.SendGridEmaiFrom, APISettings.SendGridEmaiFromName, email.Email);
        //                        }
        //                        catch (Exception ex)
        //                        {
        //                            errormsg.AppendFormat("<br/>Error happened when try to sent out notification email to {0}, <br/> message body: <br/> {1}, <br/> error message {2}<br/>", email.Email, str.ToString(), ex.Message);
        //                        }
        //                    }

        //                    errormsg.AppendFormat("Get email function stop at {0}<br/>", DateTime.Now);

        //                    if (!string.IsNullOrEmpty(errormsg.ToString()))
        //                    {
        //                        //string templatepath = System.Web.HttpContext.Current.Server.MapPath(APISettings.DocumentImportErrorMessageEmailTemplate);
        //                        //string str = File.ReadAllText(templatepath);
        //                        string str = APISettings.DocumentImportErrorMessageEmailTemplate;

        //                        str = str.Replace("{{content}}", errormsg.ToString());
        //                        str = str.Replace("{{domainpath}}", d.PrimaryUrlPath);


        //                        MailMessage message = new MailMessage();

        //                        //message.To.Add(new MailAddress(APISettings.EmailAddressForDocumentImportErrorMessage));
        //                        message.Subject = "Document Import to SharePoint Errors, from " + APISettings.Environment;
        //                        //message.IsBodyHtml = true;
        //                        message.Body = str.ToString(); ;
        //                        new EmailMessage("").SendGridEmail(message, APISettings.SendGridKey, APISettings.SendGridEmaiFrom, APISettings.SendGridEmaiFromName, APISettings.EmailAddressForDocumentImportErrorMessage);
        //                    }
        //                    return Ok();
        //                }
        //                else
        //                {
        //                    return BadRequest("Provided token is expired.");
        //                }
        //            }
        //            catch
        //            {
        //                return BadRequest("Provided token is not valid.");
        //            }
        //        }
        //        else
        //        {
        //            return BadRequest("Token not found.");
        //        }

        //    }
        //    else
        //    {
        //        return BadRequest("Configurations not setup.");
        //    }

        //}
    }
}
