﻿namespace MicroSaasWebApi.Models.AlphaCCO
{
    public class WorkflowRequest : BaseAPIRequest
    {

    }
    public class CurrentUserRequest : BaseAPIRequest
    {
    }
    public class StartWorkflowRequest : BaseAPIRequest
    {
        public string? WorkflowMasterId { get; set; }
        public string? AccountCode { get; set; }
        public string? OpenAccountType { get; set; }
    }
    public class SearchWorkflowRequest : BaseAPIRequest
    {
        public string? WorkflowKey { get; set; }
        public string? WorkflowInstanceId { get; set; }
    }
    public class AlphaCCOCardRequest : BaseAPIRequest
    {
        public string? WorkflowKey { get; set; }
    }
    public class AlphaCCOQuestionsRequest : BaseAPIRequest
    {
        public string? SurveyId { get; set; }
        public string? WorkflowKey { get; set; }
        public string? RequestBody { get; set; }
        public AlphaCCOQuestionsRequestBody? AlphaCCOQuestionsRequestBody { get; set; }
    }
    public class AlphaCCOQuestionsRequestBody
    {
        public int answerStatusId { get; set; }
        public string? comment { get; set; }
        public List<string>? comments { get; set; }
        public int id { get; set; }
        public int registerId { get; set; }
        public int requestId { get; set; }
        public Dictionary<string, Value>? values { get; set; }
    }
    public class Value
    {
        public int id { get; set; }
        public string? answer { get; set; }
        public List<string>? picklists { get; set; }
        public bool isPickList { get; set; }
        public int registerItemId { get; set; }
        public required object matrixAnswers { get; set; }
        public List<string>? documents { get; set; }
    }
    public class LoginInfoRequest : BaseAPIRequest
    {
        public string? UserId { get; set; }
    }
}
