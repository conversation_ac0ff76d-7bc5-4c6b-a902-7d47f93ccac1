trigger: none

resources:
- repo: self

variables:    
  # Container registry service connection established during pipeline creation
  dockerRegistryServiceConnection: 'uat-webapi-acr-azdvp-sc'
  imageRepository: 'uat-webapi-webapi'
  containerRegistry: 'uatwebapiiacr.azurecr.io'
  dockerfilePath: '$(Build.SourcesDirectory)/Dockerfile'  
  appSettingsFileName: "appsettings_$(client)_$(environment).json"
  dynamicTag: $(Build.BuildId)
  dynamicDescription: "Pipeline run for Client- $(client), Environment- $(environment), Tag- $(dynamicTag)"
  appSettingsEnvironment: '-ASPNETCORE_ENVIRONMENT Staging'
  webAppName: "webapi-webapi-uat" # $(environment)-$(client)-webapi-appService Hard coded for now.
  azureSubscription: "uat-webapi-apps-azdvp-sc"

stages:
- stage: Initialize
  jobs:
  - job: SetupDescription
    pool:
      vmImage: ubuntu-latest
    steps:
    - script: |
        echo "Selected Client: $(client)"
        echo "Selected Environment: $(environment)"
        echo "Current Project Path: $(System.DefaultWorkingDirectory)"
        echo "$(dynamicDescription)"
      displayName: "Print Parameters and Project Path"
#   displayName: "SonarQube Scan"
#   jobs:
#   - job: SonarQubeScan
#     displayName: "SonarQube Scan"
#     pool:
#       vmImage: ubuntu-latest
#     steps:
#     - task: JavaToolInstaller@0
#       inputs:
#         versionSpec: '17' # Specify the desired Java version here
#         jdkArchitectureOption: 'x64'
#         jdkSourceOption: 'PreInstalled'
#     - task: SonarQubePrepare@5
#       inputs:
#         SonarQube: 'SonarQubeSC'
#         scannerMode: 'MSBuild'
#         projectKey: 'WEBAPI-Cloud-Apps_MicroSaasWebApi_AY0Tsci28T8_V5xlCNmk'
#         projectName: 'MicroSaasWebApi'
#     - task: DotNetCoreCLI@2
#       inputs:
#         command: 'restore'
#         projects: '**/*MicroSaasWebApi/*.csproj'
#     - task: DotNetCoreCLI@2
#       inputs:
#         command: 'build'
#         projects: '**/*MicroSaasWebApi/*.csproj'
#     - task: SonarQubeAnalyze@5
#       inputs:
#         jdkVersion: 'JAVA_HOME'
#     - task: SonarQubePublish@5
#       inputs:
#         pollingTimeoutSec: "300"
#     - task: DotNetCoreCLI@2
#       inputs:
#         command: 'test'
#         projects: '**/*MicroSaasWebApi.Tests.Unit/*.csproj'
#         arguments: '--configuration Release --collect "Code coverage"'

- stage: Build
  # - stage: SonarQubeScan
  displayName: Build and push stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: ubuntu-latest
    steps:
    - bash: |
        echo "Application Name Environment: $(client)-webapi-$(environment)"
        echo "Application Settings FileName to be Copied: appsettings_$(client)_$(environment).json"
        echo "Current Project Path: $(System.DefaultWorkingDirectory)"
      displayName: "Print Variables."

    - script: cp MicroSaasWebApi/Assets/$(client)/AppSettings/${{ variables.appSettingsFileName }} MicroSaasWebApi/appsettings.json
      displayName: "Setup Client-Specific App Settings"
    - task: Docker@2
      displayName: Build and push an image to container registry
      inputs:
        containerRegistry: "$(dockerRegistryServiceConnection)"
        repository: "$(imageRepository)"
        command: "buildAndPush"
        Dockerfile: "$(dockerfilePath)"
        tags: "$(dynamicTag)"
    - task: AzureWebAppContainer@1
      displayName: "Deploy to $(environment)"
      inputs:
        azureSubscription: $(azureSubscription)
        appName: $(webAppName)
        containers: "$(containerRegistry)/$(imageRepository):$(dynamicTag)"
        appSettings: "$(appSettingsEnvironment)"
# - script: |
#     cd MicroSaasWebApi.Tests.Unit
#     dotnet test /p:CollectCoverage=true /p:CoverletOutputFormat=opencover /p:CoverletOutput=./coverage-reports/
#   displayName: "Run Tests and Collect Coverage"
# - task: PublishCodeCoverageResults@1
#   displayName: "Publish Code Coverage"
#   inputs:
#     codeCoverageTool: "Cobertura"
#     summaryFileLocation: "$(System.DefaultWorkingDirectory)/MicroSaasWebApi.Tests.Unit/coverage-reports/coverage.opencover.xml"
